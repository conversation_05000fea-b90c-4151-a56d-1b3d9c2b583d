import React, { useState, useCallback } from 'react';
import {
  Popper,
  Paper,
  MenuList,
  MenuItem,
  ListItemIcon,
  ListItemText,
  ClickAwayListener,
  Grow,
  Fade,
} from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronRight } from '@fortawesome/free-solid-svg-icons';

interface CascadingMenuProps {
  /** Controls whether the menu is open */
  open: boolean;
  /** An HTML element used to set the position of the popper */
  anchorEl: null | HTMLElement;
  /** Menu items and submenus */
  children: React.ReactNode;
  /** HTML element or function for portal children */
  container?: HTMLElement | (() => HTMLElement);
  /** Direction of the text */
  direction?: 'ltr' | 'rtl';
  /** Whether to disable the portal behavior */
  disablePortal?: boolean;
  /** Keep the children in the DOM for SEO or responsiveness */
  keepMounted?: boolean;
  /** Popper placement */
  placement?: 
    | 'auto-end'
    | 'auto-start'
    | 'auto'
    | 'bottom-end'
    | 'bottom-start'
    | 'bottom'
    | 'left-end'
    | 'left-start'
    | 'left'
    | 'right-end'
    | 'right-start'
    | 'right'
    | 'top-end'
    | 'top-start'
    | 'top';
  /** Support for transition */
  transition?: boolean;
  /** Callback when menu should close */
  onClose?: () => void;
}

interface CascadingSubmenuProps {
  /** Title of the submenu */
  title: string;
  /** Optional icon for the submenu */
  icon?: React.ReactNode;
  /** Submenu items */
  children: React.ReactNode;
  /** Callback when submenu item is clicked */
  onItemClick?: () => void;
}

const CascadingSubmenu: React.FC<CascadingSubmenuProps> = ({
  title,
  icon,
  children,
  onItemClick
}) => {
  const [submenuOpen, setSubmenuOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMouseEnter = useCallback((event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setSubmenuOpen(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setSubmenuOpen(false);
    setAnchorEl(null);
  }, []);

  const handleItemClick = useCallback(() => {
    setSubmenuOpen(false);
    if (onItemClick) {
      onItemClick();
    }
  }, [onItemClick]);

  return (
    <>
      <MenuItem
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        sx={{
          position: 'relative',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        }}
      >
        {icon && (
          <ListItemIcon>
            {icon}
          </ListItemIcon>
        )}
        <ListItemText>{title}</ListItemText>
        <ListItemIcon sx={{ justifyContent: 'flex-end', minWidth: 'auto', ml: 2 }}>
          <FontAwesomeIcon icon={faChevronRight} size="sm" />
        </ListItemIcon>
      </MenuItem>
      
      <Popper
        open={submenuOpen}
        anchorEl={anchorEl}
        placement="right-start"
        transition
        disablePortal={false}
        sx={{ zIndex: 1300 }}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={150}>
            <Paper
              elevation={8}
              onMouseEnter={() => setSubmenuOpen(true)}
              onMouseLeave={handleMouseLeave}
              sx={{
                ml: 0.5,
                minWidth: 150,
                maxWidth: 300,
              }}
            >
              <MenuList dense>
                {React.Children.map(children, (child) => {
                  if (React.isValidElement(child)) {
                    return React.cloneElement(child, {
                      onClick: (event: React.MouseEvent) => {
                        if ((child.props as any).onClick) {
                          (child.props as any).onClick(event);
                        }
                        handleItemClick();
                      },
                    } as any);
                  }
                  return child;
                })}
              </MenuList>
            </Paper>
          </Fade>
        )}
      </Popper>
    </>
  );
};

const CascadingMenu: React.FC<CascadingMenuProps> = ({
  open,
  anchorEl,
  children,
  container,
  direction = 'ltr',
  disablePortal = false,
  keepMounted = false,
  placement = 'bottom',
  transition = true,
  onClose,
}) => {
  const handleClickAway = useCallback(() => {
    if (onClose) {
      onClose();
    }
  }, [onClose]);

  const TransitionComponent = transition ? Grow : React.Fragment;
  const transitionProps = transition ? { timeout: 150 } : {};

  return (
    <Popper
      open={open}
      anchorEl={anchorEl}
      placement={placement}
      container={container}
      disablePortal={disablePortal}
      keepMounted={keepMounted}
      transition={transition}
      sx={{ zIndex: 1300 }}
    >
      {({ TransitionProps }) => (
        <TransitionComponent {...TransitionProps} {...transitionProps}>
          <Paper
            elevation={8}
            sx={{
              direction,
              minWidth: 150,
              maxWidth: 300,
              mt: 0.5,
            }}
          >
            <ClickAwayListener onClickAway={handleClickAway}>
              <MenuList dense>
                {React.Children.map(children, (child) => {
                  if (React.isValidElement(child)) {
                    // Se è un CascadingSubmenu, passa la callback onItemClick per chiudere il menu principale
                    if (child.type === CascadingSubmenu) {
                      return React.cloneElement(child, {
                        onItemClick: onClose,
                      } as any);
                    }
                    // Per gli altri MenuItem, aggiungi il comportamento di chiusura
                    return React.cloneElement(child, {
                      onClick: (event: React.MouseEvent) => {
                        if ((child.props as any).onClick) {
                          (child.props as any).onClick(event);
                        }
                        if (onClose) {
                          onClose();
                        }
                      },
                    } as any);
                  }
                  return child;
                })}
              </MenuList>
            </ClickAwayListener>
          </Paper>
        </TransitionComponent>
      )}
    </Popper>
  );
};

export { CascadingMenu, CascadingSubmenu };
export type { CascadingMenuProps, CascadingSubmenuProps };
