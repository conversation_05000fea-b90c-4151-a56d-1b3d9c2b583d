import React from "react";
import {
    DatePicker as VDatePicker,
    LocalizationProvider,
    AdapterDateFns,
} from "@vapor/v3-components";
import { it } from "date-fns/locale";

/**
 * Simple wrapper around Vapor's DatePicker to facilitate future upgrades.
 * This component passes values directly to Vapor DatePicker.
 * Components using this DatePicker should handle their own date processing.
 *
 * For additional props not listed below, please refer to the official
 * Vapor3 DatePicker documentation: https://d33szi5b6ku3jj.cloudfront.net/v3-react-components/develop/latest/index.html?path=/docs/datepicker-date-picker--docs
 */

interface DatePickerProps {
    // Most commonly used props (explicit for better IDE support)
    value?: any; // Date | null | string - flexible for backward compatibility
    onChange?: any; // Function to handle date changes
    label?: string; // Field label
    disabled?: boolean; // Whether the field is disabled
    readOnly?: boolean; // Whether the field is read-only
    format?: string; // Date format string
    minDate?: Date; // Minimum selectable date
    maxDate?: Date; // Maximum selectable date

    // DatePicker specific props
    view?: "day" | "month" | "year"; // The visible view
    views?: Array<"day" | "month" | "year">; // Available views
    openTo?: "day" | "month" | "year"; // View to show when picker opens
    open?: boolean; // Whether the picker is open
    onOpen?: () => void; // Callback when picker opens
    onClose?: () => void; // Callback when picker closes
    // Localization props
    localeText?: any; // Locale for components texts

    // MUI TextField integration via slotProps
    slotProps?: {
        textField?: {
            error?: boolean;
            helperText?: any; // string | boolean - flexible for error messages
            [key: string]: any;
        };
        [key: string]: any;
    };

    // Style props
    sx?: any;

    // Allow any additional Vapor DatePicker props for maximum flexibility
    [key: string]: any;
}

export const DatePicker: React.FC<DatePickerProps> = (props) => {
    // Extract the specific props and remaining props
    const {
        value,
        onChange,
        label,
        disabled,
        readOnly,
        format,
        minDate,
        maxDate,
        view,
        views,
        openTo,
        open,
        onOpen,
        onClose,
        localeText,
        slotProps,
        sx,
        ...remainingProps
    } = props;

    return (
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={it}>
            <VDatePicker
                value={value}
                onChange={onChange}
                label={label}
                disabled={disabled}
                readOnly={readOnly}
                format={format || "dd/MM/yyyy"}
                minDate={minDate}
                maxDate={maxDate}
                view={view}
                views={views}
                openTo={openTo}
                open={open}
                onOpen={onOpen}
                onClose={onClose}
                localeText={localeText}
                slotProps={slotProps}
                sx={sx}
                {...remainingProps}
            />
        </LocalizationProvider>
    );
};

export { LocalizationProvider } from "@vapor/v3-components";

export default DatePicker;
