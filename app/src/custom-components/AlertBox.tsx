import { Box } from "@vapor/react-material";
import { ReactNode, ReactElement } from "react";

type Props = {
    children: ReactNode;
    backgroundColor?: string;
    color?: string;
    textAlign?: string;
};
export const AlertBox = ({
    children,
    color,
    backgroundColor,
    textAlign,
}: Props): ReactElement => {
    return (
        <Box
            component="section"
            sx={{
                padding: "15px",
                textAlign: textAlign ? textAlign : "center",
                color: color ? color : "#3a87ad",
                backgroundColor: backgroundColor ? backgroundColor : "#d9edf7",
                width: "auto",
                height: "fit-content",
                borderColor: "#bce8f1",
            }}
        >
            {children}
        </Box>
    );
};
