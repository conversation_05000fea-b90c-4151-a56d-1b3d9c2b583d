import { FormControl, Input<PERSON>abel, TextField, ListItem } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { useEffect } from "react";
import useGetCustom from "../hooks/useGetCustom";
import { Anagraficha } from "../interfaces/documents.interface";
import { AutocompleteProps } from "@mui/material";
import { debounce } from "lodash";
import CustomAutocomplete from "./CustomAutocomplete";

export interface SearchArchiveProps {
    query: string;
}

export const useAnagraficheSearch = ({
    query
}: SearchArchiveProps): {
    data: Anagraficha[];
    error: any;
    hasLoaded: boolean;
    loading: boolean;
} => {
    const { data, doFetch, error, hasLoaded, loading } = useGetCustom("default/anagrafiche/search?noTemplateVars=true");

    const debouncedFetch = debounce((query: string) => {
        doFetch(true, {
            q: query === "" ? " " : query
        });
    }, 1000);

    useEffect(() => {
        debouncedFetch(query);

        return () => {
            debouncedFetch.cancel();
        };
    }, [query]);

    return { data, error, hasLoaded, loading };
};

interface AnagraficheSearchProps {
    query: string;
    value: string | null;
    onChange: AutocompleteProps<string, false, false, false>["onChange"];
    inputValue: string;
    onInputChange: AutocompleteProps<string, false, false, false>["onInputChange"];
    id?: string;
    width?: string | number;
}

export const AnagraficheSearch = ({ query, value, onChange, inputValue, onInputChange, id, width }: AnagraficheSearchProps) => {
    const { t } = useTranslation();

    const anagraficheSearchResponse = useAnagraficheSearch({
        query: query
    });
    return (
        <FormControl>
            <InputLabel>{t("Mitente")}</InputLabel>
            <CustomAutocomplete
                sx={{ width: width }}
                value={value}
                inputValue={inputValue}
                onInputChange={onInputChange}
                id={id}
                noOptionsText={"Nessuna pratica trovata"}
                onChange={onChange}
                loading={anagraficheSearchResponse.loading}
                options={anagraficheSearchResponse?.data?.map((value: Anagraficha) => value) || []}
                renderInput={(params: any) => <TextField placeholder={t("Cerca pratica denominazione")} {...params} />}
                getOptionLabel={(option: Anagraficha) => option.denominazione || ""}
                renderOption={(props: any, option: Anagraficha) => (
                    <ListItem {...props} value={option.id}>
                        {option.denominazione} {option.codicefiscale && `(${option.codicefiscale})`}
                    </ListItem>
                )}
            ></CustomAutocomplete>
        </FormControl>
    );
};
