import React from "react";
import { Typography, TextField } from "@vapor/react-material";
import { createFilterOptions } from "@mui/material/Autocomplete";
import { useTranslation } from "@1f/react-sdk";
import CustomAutocomplete from "./CustomAutocomplete";

const filter = createFilterOptions<any>();

interface Option {
    id?: string;
    nome?: string;
    inputValue?: string;
    [key: string]: any;
}

interface AutocompleteWithAddProps {
    options: Option[];
    value: Option | null;
    onChange: (event: React.SyntheticEvent, value: any) => void;
    onAddNew?: () => void;
    label?: string;
    placeholder?: string;
    width?: number | string;
    sx?: any;
    getOptionLabel?: (option: any) => string;
    valueField?: string;
    labelField?: string;
    disabled?: boolean;
}

const AutocompleteWithAdd: React.FC<AutocompleteWithAddProps> = ({ options = [], value, onChange, label = "", placeholder = "", width = 400, sx = {}, getOptionLabel, labelField = "nome", disabled = false }) => {
    const { t } = useTranslation();

    // Default option label getter if not provided
    const defaultGetOptionLabel = (option: any) => {
        if (typeof option === "string") {
            return option;
        }
        if (option?.nome?.startsWith(t('Aggiungi "'))) {
            return option.inputValue;
        }
        return option[labelField] || "";
    };

    // Use provided getOptionLabel or default
    const optionLabelGetter = getOptionLabel || defaultGetOptionLabel;

    return (
        <CustomAutocomplete
            sx={{ width: width, ...sx }}
            options={options}
            onChange={onChange}
            filterOptions={(options: any, params: any) => {
                const filtered = filter(options, params);
                const { inputValue } = params;
                const isExisting = options.some((option: any) => inputValue === option[labelField]);
                if (inputValue !== "" && !isExisting) {
                    filtered.push({
                        inputValue: inputValue,
                        [labelField]: `${t('Aggiungi "')}${inputValue}"`
                    });
                }
                return filtered;
            }}
            selectOnFocus
            clearOnBlur
            getOptionLabel={optionLabelGetter}
            value={value}
            disabled={disabled}
            renderOption={(props: any, option: any) => {
                if (option[labelField] && option[labelField].startsWith(t('Aggiungi "'))) {
                    return (
                        <li {...props}>
                            <Typography>{option[labelField]}</Typography>
                        </li>
                    );
                }
                return <li {...props}>{option[labelField]}</li>;
            }}
            renderInput={(params: any) => <TextField {...params} label={label} placeholder={placeholder} />}
        />
    );
};

export default AutocompleteWithAdd;
