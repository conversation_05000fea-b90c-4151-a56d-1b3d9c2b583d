import { <PERSON>, Button, Select, MenuItem } from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import TodayIcon from "@mui/icons-material/Today";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import {
    ICalendarButtonsProps,
} from "../features/agenda/generalCalendar/typings/generalCalendar.interface";
import { gettingCalendarViewName, gettingCalendarView } from "../features/agenda/generalCalendar/helpers/gettingCalendarViewName";
import { updateCalendar } from "../features/agenda/generalCalendar/helpers/calendarHelper";

export default function CalendarCustomButtons(props: ICalendarButtonsProps) {
    const {
        query,
        setQuery,
        calendarRef,
        monthTitle,
        setMonthTitle,
        t,
        children,
        selectedView,
        onViewChange,
    } = props;

    const handleNavigationClick = (action: any) => {
        const calendarAPI: any = calendarRef?.current?.getApi();
        if (calendarAPI) {
            action(calendarAPI);
            updateCalendar(
                calendarAPI,
                query,
                setQuery,
                setMonthTitle,
                t,
                gettingCalendarViewName(calendarAPI.currentData.currentViewType)
            );
        }
    };

    const changeView = (viewName: string) => {
        const calendarAPI = calendarRef?.current?.getApi();
        if (calendarAPI) {
            calendarAPI.changeView(viewName);
            setQuery({ ...query, viewName: viewName });
            updateCalendar(
                calendarAPI,
                { ...query, viewName: viewName },
                setQuery,
                setMonthTitle,
                t,
                gettingCalendarViewName(viewName)
            );
        }
    };

    return (
        <Box sx={{ p: 2, display: "flex", alignItems: "center" }}>
            {/* Left buttons */}
            <Box sx={{ flexGrow: 1 }}>
                <Button
                    variant="outlined"
                    onClick={() =>
                        handleNavigationClick((api: any) => api.prevYear())
                    }
                    sx={{ mr: 1 }}
                >
                    <KeyboardDoubleArrowLeftIcon />
                </Button>
                <Button
                    variant="outlined"
                    onClick={() =>
                        handleNavigationClick((api: any) => api.prev())
                    }
                    sx={{ mr: 1 }}
                >
                    <KeyboardArrowLeftIcon />
                </Button>
                <Button
                    variant="outlined"
                    onClick={() =>
                        handleNavigationClick((api: any) => api.next())
                    }
                    sx={{ mr: 1 }}
                >
                    <KeyboardArrowRightIcon />
                </Button>
                <Button
                    variant="outlined"
                    onClick={() =>
                        handleNavigationClick((api: any) => api.nextYear())
                    }
                    sx={{ mr: 1 }}
                >
                    <KeyboardDoubleArrowRightIcon />
                </Button>
                <Button
                    variant="outlined"
                    endIcon={<TodayIcon />}
                    onClick={() =>
                        handleNavigationClick((api: any) => api.today())
                    }
                >
                    {t("Oggi")}
                </Button>
            </Box>

            {/* Center title */}
            <Typography
                variant="titleMedium"
                component="div"
                color="primary.textTitleColor"
                sx={{ flexGrow: 1, textAlign: "center" }}
            >
                {monthTitle}
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', ml: 3 }}>
                <Select
                    value={gettingCalendarView(selectedView || "month")}
                    onChange={(e: any) => {
                        changeView(e.target.value);
                        if (onViewChange) {
                            onViewChange(gettingCalendarViewName(e.target.value));
                        }
                    }}
                    variant="outlined"
                    sx={{ minWidth: 200 }}
                >
                    <MenuItem value="dayGridMonth">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {t("Mese")}
                        </Box>
                    </MenuItem>
                    <MenuItem value="timeGridWeek">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {t("Settimana")}
                        </Box>
                    </MenuItem>
                    <MenuItem value="timeGridWorkWeek">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {t("Settimana lavorativa")}
                        </Box>
                    </MenuItem>
                    <MenuItem value="timeGridDay">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {t("Giorno")}
                        </Box>
                    </MenuItem>
                </Select>
            </Box>

            <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                {children}
            </Box>
        </Box>
    );
}
