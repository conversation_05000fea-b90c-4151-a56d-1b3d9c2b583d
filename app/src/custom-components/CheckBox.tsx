import { FormControlLabel, Checkbox } from "@vapor/react-material";

import { Controller } from "react-hook-form";

export const ControlledCheckbox = ({ name, label, control,style = null, ...props }: any) => {
    
    return (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <FormControlLabel
                    control={
                        <Checkbox
                            {...field}
                            checked={
                                field.value === "1" || field.value === true
                            }
                            {...props}
                            sx={style}
                        />
                    }
                    label={label}
                />
            )}
        />
    );
};
