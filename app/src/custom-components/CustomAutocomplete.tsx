import React, { useState } from "react";
import { Autocomplete, styled } from "@vapor/v3-components";
import { useTranslation } from "@1f/react-sdk";

interface CustomAutocompleteProps {
    renderInput: any;
    options: any;
    [key: string]: any; // To allow passing additional props
}

const StyledAutocomplete = styled(Autocomplete)({
    "& .MuiOutlinedInput-root": {
        paddingBottom: "0px !important", // Override padding
        paddingTop: "0px !important" // Override padding
    },
    "& .MuiAutocomplete-endAdornment": {
        padding: "9px !important",
        right: "9px"
    }
});
const CustomAutocomplete: React.FC<CustomAutocompleteProps> = ({ options, renderInput, ...rest }) => {
    const { t } = useTranslation();
    const [open, setOpen] = useState(false);
    return (
        <StyledAutocomplete
            disableClearable
            options={options}
            renderInput={renderInput}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
            componentsProps={{
                popupIndicator: {
                    title: open ? t("Chiudi") : t("Apri")
                }
            }}
            slotProps={{
                popper: {
                    modifiers: [
                        {
                            name: "preventOverflow",
                            options: {
                                boundary: "viewport" // Ensure the tooltip stays within the viewport
                            }
                        }
                    ]
                }
            }}
            loadingText={t("Caricamento...")}
            noOptionsText={t("Nessuna opzione")}
            disablePortal
            {...rest} // Pass additional props dynamically
        />
    );
};

export default CustomAutocomplete;
