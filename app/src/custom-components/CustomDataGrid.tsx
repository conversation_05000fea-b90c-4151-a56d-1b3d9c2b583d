import React, { useState, useCallback, useEffect } from "react";
import { DataGridPro } from "@vapor/v";
import {
    GridColDef,
    GridPaginationModel,
    GridCallbackDetails,
    GridFeatureMode,
    GridSortModel,
    GridRowSelectionModel,
    GridToolbarContainer,
    GridToolbarColumnsButton,
    GridRowParams,
    useGridApiRef
} from "@mui/x-data-grid-pro";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSortUp, faSortDown, faTableColumns } from "@fortawesome/free-solid-svg-icons";
import { useDataGridConfig } from "../services/DataGridConfigService";
import { getColumnWidthsFromConfig, getVisibilityModelFromConfig } from "../helpers/dataGridColumnsHandler";

interface IDataGrid {
    data: any;
    columns: GridColDef[];
    pageSizeOptions?: number[];
    onPageChangeCallback?: (model: GridPaginationModel, details: GridCallbackDetails<any>) => void;
    name?: string;
    section?: string;
    paginationMode?: GridFeatureMode | undefined;
    totalRows: number;
    onClickKey?: string;
    onClickCheckboxKey?: string;
    sortingMode?: GridFeatureMode | undefined;
    page: number | undefined;
    pageSize: number;
    loading?: boolean;
    selectableRows?: boolean;
    onColumnOrderChange?: (allColumns: any) => void;
    onColumnWidthChange?: (allColumns: any) => void;
    onColumnVisibilityModelChange?: (allColumns: any) => void;
    onRowSelectionModelChange?: (rowSelectionModel: GridRowSelectionModel, details: GridCallbackDetails<any>) => void;
    onClickCallback?: (uniqueid: any) => void;
    query?: any;
    setQuery?: React.Dispatch<React.SetStateAction<any>>;
    hideFooter?: boolean;
    disableColumnResize?: boolean;
    disableColumnReorder?: boolean;
    disableColumnMenu?: boolean;
    hasAdditionaStyles?: boolean;
    isRowSelectableCustom?: any;
    rowReordering?: boolean;
    onRowOrderChange?: (newOrder: any) => void;
    additionStyles?: any;
    hideToolbar?: boolean;
    disablePagination?: boolean;
    disableSorting?: boolean;
    hidePaginationText?: boolean;
    sortModel?: GridSortModel;
    onSortModelChange?: any;
}

export const CustomDataGrid = (props: IDataGrid) => {
    const [config, setConfig] = useState<any>(null);

    const {
        columns,
        data,
        totalRows,
        selectableRows,
        loading,
        onClickKey = "uniqueid",
        page,
        pageSize,
        onClickCallback,
        query,
        setQuery,
        onColumnWidthChange,
        pageSizeOptions,
        onPageChangeCallback,
        paginationMode,
        sortingMode,
        onRowSelectionModelChange,
        onColumnOrderChange,
        onColumnVisibilityModelChange,
        hideFooter = false,
        disableColumnResize = false,
        disableColumnReorder = false,
        disableColumnMenu = false,
        name,
        isRowSelectableCustom,
        hasAdditionaStyles = true,
        rowReordering,
        onRowOrderChange,
        additionStyles,
        hideToolbar = false,
        disablePagination = false,
        disableSorting = false,
        hidePaginationText = false,
        sortModel,
        onSortModelChange
    } = props;

    const apiRef = useGridApiRef();
    const { saveGridConfig, getGridConfig } = useDataGridConfig();

    const [columnVisibilityModel, setColumnVisibilityModel] = useState({});
    const [processedColumns, setProcessedColumns] = useState<GridColDef[]>([]);
    const [configReady, setConfigReady] = useState(false);
    const defaultPageSizeOptions = [10, 20, 50];
    const defaultpaginationMode = "server";
    const defaultSortingMode = "server";

    useEffect(() => {
        const loadGridConfig = async () => {
            if (name) {
                setConfigReady(false);
                try {
                    const config = await getGridConfig(name);

                    if (config) {
                        setConfig(config);
                    }

                    if (config?.columns_config) {
                        // Use helper function instead of inline logic
                        setColumnVisibilityModel(getVisibilityModelFromConfig(config));
                    }
                } catch (error) {
                    console.error("Error loading grid configuration:", error);
                    // Mark as ready even if there's an error
                    setConfigReady(true);
                }
            } else {
                // If no name is provided, mark as ready immediately
                setConfigReady(true);
            }
        };

        loadGridConfig();
    }, [name]);

    // Process columns with configuration and sorting settings
    useEffect(() => {
        let finalColumns = [...columns];

        // First, apply sorting disable if needed
        if (disableSorting) {
            finalColumns = finalColumns.map((col) => ({
                ...col,
                sortable: false
            }));
        }

        // Then apply column configuration if available
        if (config?.columns_config) {
            // Use helper function to get updated columns with proper widths and configurations
            const updatedColumns = getColumnWidthsFromConfig(config, finalColumns);

            if (updatedColumns.length > 0) {
                // Create a map for quick lookup of updated columns
                const updatedColumnsMap = new Map(updatedColumns.map((col) => [col.field, col]));

                // Apply updates to final columns
                finalColumns = finalColumns.map((col) => {
                    const updatedCol = updatedColumnsMap.get(col.field);
                    return updatedCol || col;
                });
            }

            // Use helper function to sort columns according to config
            const configFields = config.columns_config.map((col: any) => col.field);

            // Add checkbox column to config fields if selectableRows is true
            if (selectableRows && !configFields.includes("__check__")) {
                configFields.unshift("__check__");
            }

            // Sort columns using helper function
            finalColumns = finalColumns.sort((a, b) => {
                const indexA = configFields.indexOf(a.field);
                const indexB = configFields.indexOf(b.field);

                if (indexA === -1) return 1;
                if (indexB === -1) return -1;

                return indexA - indexB;
            });
        }

        setProcessedColumns(finalColumns);
        setConfigReady(true);
    }, [config, columns, disableSorting, selectableRows]);

    const getRowIdCustom = (row: any) => {
        // Handle null or undefined rows
        if (!row || typeof row !== "object") return Math.random().toString(36).slice(2, 7);
        if (onClickKey === "custom") return Math.random().toString(36).slice(2, 7);
        const id = row[onClickKey];
        // Ensure we always return a valid string ID
        return id ? String(id) : Math.random().toString(36).slice(2, 7);
    };

    const onSortChange = useCallback(
        (sortModelParam: GridSortModel) => {
            // Check if custom sort handler is provided (for Activities component)
            if (onSortModelChange) {
                onSortModelChange(sortModelParam);
                return;
            }

            // Default sort handling for other components
            if (setQuery !== undefined) {
                let newSortColumn = sortModelParam.length > 0 ? sortModelParam[0].field : query.sortColumn;
                let newSortOrder = sortModelParam.length > 0 ? sortModelParam[0].sort ?? "desc" : query.sortOrder;

                if (newSortColumn !== query.sortColumn || newSortOrder !== query.sortOrder) {
                    setQuery({
                        ...query,
                        sortColumn: newSortColumn,
                        sortOrder: newSortOrder
                    });
                }
            }
        },
        [setQuery, query, onSortModelChange]
    );

    // Simplified getColumnJson function - now more focused on configuration format
    const getColumnJson = (columns: any, visibilityModel?: any) => {
        const columnJson = columns.map(function (value: any) {
            let returnColumn: any = {
                field: value.field,
                headerName: value.headerName,
                flex: value.flex,
                sortable: value.sortable,
                hideable: value.hideable,
                width: value.width,
                minWidth: value.minWidth
            };

            // Special handling for checkbox column
            if (value.field === "__check__") {
                returnColumn.width = 30;
                returnColumn.sortable = false;
                returnColumn.hideable = false;
                returnColumn.resizable = false;
                returnColumn.hide = false;
                returnColumn.disableReorder = true;
            }

            // Special handling for action column
            if (value.field === "Azioni") {
                returnColumn.width = 120;
                returnColumn.sortable = false;
                returnColumn.hideable = false;
                returnColumn.resizable = false;
                returnColumn.hide = false;
                returnColumn.disableReorder = true;
            }

            // Apply visibility model if provided
            if (visibilityModel) {
                returnColumn.hide = visibilityModel[value.field] !== undefined && visibilityModel[value.field] === false;
            }

            // Preserve renderCell and valueGetter
            if (value.renderCell !== undefined) returnColumn.renderCell = value.renderCell;
            if (value.valueGetter !== undefined) returnColumn.valueGetter = value.valueGetter;

            return returnColumn;
        });
        return columnJson;
    };

    const handleColumnOrderChange = async () => {
        const allColumn = apiRef.current.getAllColumns();

        // Ensure __check__ and action columns stay in their fixed positions
        const reorderedColumns = [...allColumn];

        // Find and fix __check__ column position (should be first)
        const checkColumnIndex = reorderedColumns.findIndex((col) => col.field === "__check__");
        if (checkColumnIndex > 0) {
            const checkColumn = reorderedColumns.splice(checkColumnIndex, 1)[0];
            reorderedColumns.unshift(checkColumn);
        }

        // Find and fix action column position (should be last)
        const actionColumnIndex = reorderedColumns.findIndex((col) => col.field === "Azioni");
        if (actionColumnIndex >= 0 && actionColumnIndex !== reorderedColumns.length - 1) {
            const actionColumn = reorderedColumns.splice(actionColumnIndex, 1)[0];
            reorderedColumns.push(actionColumn);
        }

        // Update the grid with the corrected order
        if (JSON.stringify(reorderedColumns.map((col) => col.field)) !== JSON.stringify(allColumn.map((col) => col.field))) {
            for (let i = 0; i < reorderedColumns.length; i++) {
                apiRef.current.setColumnIndex(reorderedColumns[i].field, i);
            }
        }

        const columnConfig = getColumnJson(reorderedColumns);

        if (name) {
            try {
                await saveGridConfig({
                    columns_config: columnConfig,
                    name,
                    id: config?.id
                });
            } catch (error) {
                console.error("Error saving column order:", error);
            }
        }

        if (onColumnOrderChange !== undefined) {
            onColumnOrderChange(columnConfig);
        }
    };

    const handleColumnWidthChange = async () => {
        const allColumns = apiRef.current.getAllColumns();

        const adjustedColumns = allColumns.map((column: any) => {
            const width = column.width || 0;

            // Special handling for checkbox column - keep it at fixed width
            if (column.field === "__check__") {
                return {
                    ...column,
                    width: 30,
                    minWidth: 30,
                    resizable: false
                };
            }

            // Special handling for action column - keep it at fixed width
            if (column.field === "Azioni") {
                return {
                    ...column,
                    width: 120,
                    minWidth: 120,
                    resizable: false
                };
            }

            return {
                ...column,
                width: width < 100 ? 100 : width,
                minWidth: 100
            };
        });

        if (adjustedColumns.some((col: any, idx: number) => col.width !== allColumns[idx].width)) {
            apiRef.current.updateColumns(adjustedColumns);
        }

        const columnConfig = getColumnJson(adjustedColumns, columnVisibilityModel);

        if (name) {
            try {
                await saveGridConfig({
                    columns_config: columnConfig,
                    name,
                    id: config?.id
                });
            } catch (error) {
                console.error("Error saving column widths:", error);
            }
        }

        if (onColumnWidthChange !== undefined) {
            onColumnWidthChange(columnConfig);
        }
    };

    const handleColumnVisibilityModelChange = async (newModel: any) => {
        const allColumn = apiRef.current.getAllColumns();
        const columnConfig = getColumnJson(allColumn, newModel);

        setColumnVisibilityModel(newModel);

        if (name) {
            try {
                await saveGridConfig({
                    columns_config: columnConfig,
                    name,
                    id: config?.id
                });
            } catch (error) {
                console.error("Error saving column visibility:", error);
            }
        }

        if (onColumnVisibilityModelChange !== undefined) {
            onColumnVisibilityModelChange(columnConfig);
        }
    };

    const onClickCallbackCustom = (data: any) => {
        // Handle null or undefined data/row
        if (!data || !data.row) return;
        const uniqueid = onClickKey === "row" ? data["row"] : data["row"][onClickKey];
        if (onClickCallback !== undefined && uniqueid) onClickCallback(uniqueid);
    };

    let defaultStyles =
        data?.length > 0
            ? { display: "grid", width: "100%" }
            : {
                  display: "grid",
                  width: "100%",
                  minHeight: "230px",
                  "&.MuiDataGrid-root .MuiDataGrid-virtualScroller": {
                      overflowY: "hidden"
                  }
              };

    // Don't render the grid until configuration is ready (if name is provided)
    if (name && !configReady) {
        return <div style={{ minHeight: "230px", display: "flex", alignItems: "center", justifyContent: "center" }}>{/* You can add a loading spinner here if needed */}</div>;
    }

    return (
        <DataGridPro
            apiRef={apiRef}
            rows={(data || []).filter((row: any) => row !== null && typeof row === "object")}
            sx={hasAdditionaStyles ? defaultStyles : additionStyles !== undefined ? additionStyles : {}}
            columns={processedColumns}
            pagination={!disablePagination}
            pageSizeOptions={pageSizeOptions !== undefined ? pageSizeOptions : defaultPageSizeOptions}
            paginationModel={{ page: page ?? 0, pageSize: pageSize }}
            onPaginationModelChange={onPageChangeCallback}
            paginationMode={paginationMode !== undefined ? paginationMode : defaultpaginationMode}
            rowCount={totalRows}
            getRowId={(row) => getRowIdCustom(row) || Math.random().toString(36).slice(2, 7)}
            sortingOrder={disableSorting ? [] : ["asc", "desc"]}
            sortingMode={disableSorting ? undefined : rowReordering ? "client" : sortingMode !== undefined ? sortingMode : defaultSortingMode}
            sortModel={
                sortModel !== undefined
                    ? sortModel
                    : rowReordering
                    ? []
                    : [
                          {
                              field: query?.sortColumn,
                              sort: query?.sortOrder
                          }
                      ]
            }
            onSortModelChange={disableSorting ? undefined : onSortChange}
            loading={loading}
            // Enable checkbox selection for Activities
            checkboxSelection={selectableRows}
            isRowSelectable={
                isRowSelectableCustom !== undefined
                    ? (params: GridRowParams) => isRowSelectableCustom(params)
                    : () => {
                          // For Activities, always allow checkbox selection
                          if (name === "activitysList" || name === "activitysListCompleted") {
                              return true;
                          }
                          return !rowReordering;
                      }
            }
            disableRowSelectionOnClick={false}
            onRowSelectionModelChange={(newSelection, details) => {
                if (name === "activitysList" && onRowSelectionModelChange) {
                    onRowSelectionModelChange(newSelection, details);
                } else if (onRowSelectionModelChange) {
                    onRowSelectionModelChange(newSelection, details);
                }
            }}
            keepNonExistentRowsSelected={false}
            onColumnOrderChange={disableColumnReorder ? undefined : handleColumnOrderChange}
            columnVisibilityModel={columnVisibilityModel}
            onColumnVisibilityModelChange={handleColumnVisibilityModelChange}
            onRowClick={rowReordering ? undefined : onClickCallbackCustom}
            slots={{
                columnSortedAscendingIcon: () => <FontAwesomeIcon icon={faSortUp} />,
                columnSortedDescendingIcon: () => <FontAwesomeIcon icon={faSortDown} />,
                toolbar: () =>
                    hideToolbar || name == "activitysList" || name == "activitysListCompleted" ? null : (
                        <GridToolbarContainer>
                            <GridToolbarColumnsButton slotProps={{ button: { startIcon: <FontAwesomeIcon icon={faTableColumns} /> } }} />
                        </GridToolbarContainer>
                    )
            }}
            hideFooter={hideFooter}
            disableColumnResize={disableColumnResize}
            disableColumnReorder={disableColumnReorder}
            disableColumnMenu={disableColumnMenu}
            onColumnWidthChange={disableColumnResize ? undefined : handleColumnWidthChange}
            disableColumnPinning
            disableColumnFilter
            disableColumnSelector={name === "activitysList" || name === "activitysListCompleted"}
            localeText={{
                noRowsLabel: "Nessun elemento",
                columnMenuSortAsc: "Ordine ascendente",
                columnMenuSortDesc: "Ordine discendente",
                columnMenuHideColumn: "Nascondi colonna",
                columnMenuShowColumns: "Mostra colonne",
                columnMenuManageColumns: "Gestisci colonne",

                checkboxSelectionHeaderName: "Checkbox",
                columnsManagementSearchTitle: "Cerca",
                columnsManagementShowHideAllText: "Mostra/Nascondi tutte",
                columnsManagementReset: "Resetta",

                footerRowSelected: (count) => (hidePaginationText ? "" : `${count}  elementi selezionati`),
                footerTotalRows: hidePaginationText ? "" : "Elementi per pagina",
                footerTotalVisibleRows: (total: any, visible: any) => (hidePaginationText ? "" : `${visible} di ${total}`),
                MuiTablePagination: {
                    labelRowsPerPage: hidePaginationText ? "" : "Elementi per pagina",
                    labelDisplayedRows: ({ from, to, count }) => (hidePaginationText ? "" : `${from}-${to} di ${count}`)
                },
                toolbarColumns: "Gestisci colonne",
                toolbarColumnsLabel: "Gestisci colonne",
                columnHeaderSortIconLabel: "Ordina"
            }}
            rowReordering={rowReordering}
            onRowOrderChange={onRowOrderChange}
        />
    );
};
