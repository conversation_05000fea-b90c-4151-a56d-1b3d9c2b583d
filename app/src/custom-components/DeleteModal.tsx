import * as React from 'react';
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@vapor/react-material';

const Delete = ({ handleDelete }: any) => {
    const [open, setOpen] = React.useState(false);

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const deleteAction = async () => {
        await handleDelete();
        setOpen(false);
    }

    return (
        <React.Fragment>
            <Button variant="outlined" color='error' onClick={handleClickOpen}>
                Elimina
            </Button>
            <Dialog
                open={open}
                onClose={handleClose}
                fullWidth={true}
                maxWidth='sm'
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">
                    Delete Action
                </DialogTitle>
                <DialogContent dividers>
                    <DialogContentText id="alert-dialog-description">
                        Are you sure want to Delete
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose} variant="outlined">No</Button>
                    <Button onClick={deleteAction} variant="contained" autoFocus>
                        Yes, Delete
                    </Button>
                </DialogActions>
            </Dialog>
        </React.Fragment>
    );
}
export default Delete;