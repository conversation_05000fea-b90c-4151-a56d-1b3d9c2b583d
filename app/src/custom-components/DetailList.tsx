
import { Box, Typography } from "@vapor/react-material";
import { AnchorMenu, AnchorMenuItem } from "@vapor/react-custom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface DetaiListProps {
    title: string;
    list: any[];
    startIcon?: any;
    endIcon?: any;
    startI?: any;
    scrollToSection?: (ref: React.RefObject<HTMLDivElement>) => void;
    ref?: React.RefObject<HTMLDivElement>;
    currentSection?: string | undefined;
}

const DetailList = (props: DetaiListProps) => {
    const { title, list, scrollToSection } = props;

    return (
        <Box sx={{ mt: 2, mb: 2, backgroundColor: "transparent" }}>
            <Typography
                variant="titleSmall"
                component="div"
                color="primary.textTitleColor"
                gutterBottom
            >
                {title ?? ""}
            </Typography>
            <AnchorMenu
                aria-label="anchor menu"
                sx={{
                    "&&& .MuiTreeItem-content": {
                        backgroundColor: "#f7f7f7",
                    },
                    "&&&& .Mui-selected": {
                        backgroundColor: "hsl(200, 100%, 92%) !important",
                    },
                }}
            >
                {list.map(({ id, label, startIcon, endIcon, ref }: any) => {
                    const menuItemProps = {
                        key: id,
                        label: label,
                        nodeId: id,
                        itemId: id,
                        startIcon: startIcon ? (
                            <FontAwesomeIcon
                                width={"20px"}
                                icon={startIcon}
                            />
                        ) : undefined,
                        endIcon: endIcon ? (
                            <FontAwesomeIcon
                                icon={endIcon}
                                style={{ color: "#1e88e5" }}
                            />
                        ) : undefined,
                        onClick: () => {
                            if (ref) {
                                scrollToSection && scrollToSection(ref);
                            }
                        }
                    };

                    return <AnchorMenuItem {...menuItemProps} />;
                })}
            </AnchorMenu>
        </Box>
    );
};

export default DetailList;
