import { useApi } from "../store/ApiStore";
import { BASE_URN, DEFAULT_SUBDOMAIN } from "../utilities/constants";

interface IGetImages {
    path: string;
    id?: string;
    alt?: string;
    width?: number;
    height?: number;
    style?: any;
}

const useGetImage = ({ id, path, alt, width, height, style }: IGetImages) => {
    const { api }: any = useApi();
    const usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;

    return (
        <>
            <img id={id} src={`/api/${BASE_URN}/${usedSubdomain}${path}`} alt={alt} width={width} height={height} style={style} />
        </>
    )
}
export default useGetImage;