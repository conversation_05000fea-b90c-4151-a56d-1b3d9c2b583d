import * as React from 'react';
import { But<PERSON> } from '@vapor/react-material';
import { useTranslation } from "@1f/react-sdk";

import MoreVertIcon from '@mui/icons-material/MoreVert';

import { Box, List, ListItemText, Divider, Typography, Popover } from '@vapor/react-material';

export default function Legenda() {
    const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);

    const { t } = useTranslation();

    const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handlePopoverClose = () => {
        setAnchorEl(null);
    };

    const open = Boolean(anchorEl);

    return (
        <div>
            <Button
                aria-owns={open ? 'mouse-over-popover' : undefined}
                aria-haspopup="true"
                onMouseEnter={handlePopoverOpen}
                onMouseLeave={handlePopoverClose}
                variant="contained"
            >
                {t("Legenda")} <MoreVertIcon />
            </Button>
            <Popover
                id="mouse-over-popover"
                sx={{
                    pointerEvents: 'none',
                }}
                open={open}
                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                onClose={handlePopoverClose}
                disableRestoreFocus
            >
                <Box sx={{ borderRadius: 4 }}>
                    <Typography sx={{ p: 1 }}>{t("Legenda utenti")}</Typography>
                    <Divider />
                    <List disablePadding sx={{
                        width: 220
                    }}>
                        <Box sx={{ display: "flex" }}>
                            <ListItemText sx={{ p: 1 }} primary={t("Utente nello studio assaciato")} />
                        </Box>
                    </List>
                </Box>
            </Popover>
        </div>
    );
}