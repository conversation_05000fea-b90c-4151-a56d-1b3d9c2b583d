import React from "react";
import {
    NotificationOverflow,
    NotificationOverflowProps,
    NotificationBanner,
    NotificationInline,
    AlertTitle,
    AlertProps
} from "@vapor/v3-components";

type NotificationVariant = "overflow" | "banner" | "inline";

interface BaseNotificationProps {
    showNotification: boolean;
    text: string;
    notificationVariant?: NotificationVariant;
    severity?: AlertProps["severity"];
    variant?: AlertProps["variant"];
    action?: React.ReactNode;
    title?: string;
    icon?: React.ReactNode;
    sx?: any;
}

interface NotificationOverflowSpecificProps {
    rightButtons?: NotificationOverflowProps["rightButtons"];
    leftButtons?: NotificationOverflowProps["leftButtons"];
}

interface IBottomToastNotification extends BaseNotificationProps, NotificationOverflowSpecificProps {
    [key: string]: any; 
}

const BottomToastNotification: React.FC<IBottomToastNotification> = ({
    showNotification,
    text,
    notificationVariant = "overflow",
    severity,
    variant = "outlined",
    action,
    title,
    icon,
    sx,
    rightButtons = [],
    leftButtons = [],
    ...otherProps
}) => {
    if (!showNotification) return null;

    const notificationContent = (
        <>
            {title && <AlertTitle>{title}</AlertTitle>}
            {!title && text && <AlertTitle>{text}</AlertTitle>}
            {title && text && <div>{text}</div>}
        </>
    );

    const getActionForNonOverflow = () => {
        if (action) return action; 

        const leftButtonsArray = Array.isArray(leftButtons) ? leftButtons : [];
        const rightButtonsArray = Array.isArray(rightButtons) ? rightButtons : [];
        const allButtons = [...leftButtonsArray, ...rightButtonsArray];

        if (allButtons.length > 0) {
            return (
                <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    {allButtons.map((button, index) => (
                        <span key={index}>{button}</span>
                    ))}
                </div>
            );
        }

        return undefined;
    };

    const commonProps = {
        severity,
        variant,
        icon,
        sx,
        ...otherProps
    };

    const renderNotification = () => {
        switch (notificationVariant) {
            case "banner":
                return (
                    <NotificationBanner
                        {...commonProps}
                        action={getActionForNonOverflow()}
                    >
                        {notificationContent}
                    </NotificationBanner>
                );

            case "inline":
                return (
                    <NotificationInline
                        {...commonProps}
                        action={getActionForNonOverflow()}
                    >
                        {notificationContent}
                    </NotificationInline>
                );

            case "overflow":
            default:
                return (
                    <NotificationOverflow
                        {...commonProps}
                        action={action} 
                        rightButtons={rightButtons}
                        leftButtons={leftButtons}
                    >
                        {notificationContent}
                    </NotificationOverflow>
                );
        }
    };

    return (
        <div>
            {renderNotification()}
        </div>
    );
};

export default BottomToastNotification;
