import React from "react";
import { Title } from "@vapor/v3-components";
import { Box, Button } from "@vapor/react-material";
import { Breadcrumbs } from "@vapor/react-material";
import { Typography } from "@vapor/v3-components";
import { Link } from "react-router-dom";
import { <PERSON>leR<PERSON>, ArrowLeft } from "@vapor/react-icons";
import { useNavigate } from "react-router-dom";
import { SxProps } from "@mui/system";
import { Menu, ListItemButton } from "@vapor/react-material";
import { options } from "@fullcalendar/core/preact.js";
import SpinnerButton from "./SpinnerButton";

const DropDownExport = (props: any) => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const { button } = props;

    const handleMenu = (event: any) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };
    const getId = (index: number) => {
        const validateIndex: any = {
            0: "-1",
            1: "0",
            2: "1",
        };
        return validateIndex[index];
    };

    return (
        <>
            <Button
                variant={button.variant || "outlined"}
                onClick={handleMenu}
                sx={{ mr: "1rem" }}
                startIcon={button.startIcon}
                endIcon={button.endIcon}
            >
                {button.label}
            </Button>
            <Menu
                id="menu-appbar"
                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                }}
                keepMounted
                transformOrigin={{
                    vertical: "top",
                    horizontal: "left",
                }}
                open={Boolean(anchorEl)}
                onClose={handleClose}
            >
                {button.options.map((option: any, index: number) => (
                    <ListItemButton
                        key={index}
                        onClick={() => option.handleClick(getId(index))}
                    >
                        {option.label}
                    </ListItemButton>
                ))}
            </Menu>
        </>
    );
};

interface IPageTitle {
    title: string;
    description?: string;
    pathToPrevPage?: string;
    showBackButton?: boolean;
    actionButtons?: (IButtonTitle | React.ReactNode)[];
    children?: React.ReactNode;
    devider?: boolean;
    reduced?: boolean;
    breadcrumbs?: IBreadcrumbs[];
    size?: "extraSmall" | "medium" | "small";
    titleItems?: string[];
    handleBackNavigation?: (parameter?: any) => void;
    stateOptions?: any;
}

interface IButtonTitle {
    label: string;
    onclick: (param?: any) => void;
    type?: "button" | "submit" | "reset" | undefined;
    variant?: "text" | "outlined" | "contained";
    disabled?: boolean;
    color?: "primary" | "secondary" | "error";
    size?: "small" | "medium" | "large";
    startIcon?: React.ReactNode;
    endIcon?: React.ReactNode;
    sx?: SxProps;
    dropdown?: boolean;
    options?: any;
    isLoading?: boolean;
}

interface IBreadcrumbs {
    label: string;
    active: boolean;
    path?: string;
}

export default function PageTitle(props: IPageTitle) {
    const {
        title,
        description,
        pathToPrevPage = "",
        showBackButton = true,
        actionButtons = [],
        devider = true,
        reduced,
        breadcrumbs = [],
        size = "small",
        titleItems,
        handleBackNavigation,
        stateOptions,
    } = props;

    const navigate = useNavigate();

    const GoBackButton = () => {
        const handleBackClick = () => {
            if (handleBackNavigation) {
                handleBackNavigation();
            }
            if (!options) {
                navigate(pathToPrevPage); // Always call navigate
            } else {
                navigate(pathToPrevPage, { state: { ...stateOptions } });
            }
        };

        return (
            <Button onClick={handleBackClick}>
                <ArrowLeft />
            </Button>
        );
    };

    const ActionButtons = () => {
        const isButtonTitle = (
            button: IButtonTitle | React.ReactNode
        ): button is IButtonTitle => {
            return (button as IButtonTitle).label !== undefined;
        };

        return (
            <>
                {actionButtons.map(
                    (button: IButtonTitle | React.ReactNode, index: number) => {
                        return React.isValidElement(button) ? (
                            <Box key={index} sx={{ marginRight: "10px" }}>
                                {button}
                            </Box>
                        ) : button !== null &&
                          button !== undefined &&
                          isButtonTitle(button) ? (
                            button.dropdown ? (
                                <DropDownExport key={index} button={button} />
                            ) : (
                                <SpinnerButton
                                    key={index}
                                    type={button.type}
                                    variant={button.variant || "outlined"}
                                    onClick={button.onclick}
                                    disabled={button.disabled}
                                    color={button.color}
                                    size={button.size}
                                    startIcon={button.startIcon}
                                    endIcon={button.endIcon}
                                    sx={{ ...button.sx, marginRight: "10px" }}
                                    label={button.label}
                                    isLoading={button.isLoading ?? false}
                                />
                            )
                        ) : null;
                    }
                )}
            </>
        );
    };

    const Breadcurmbs = () => {
        const breadCrumbLength = breadcrumbs.filter((i: any) => !i.active);
        if (breadCrumbLength.length === 1) {
            throw new Error(
                "To include breadcrumbs, you must have more than one link."
            );
        }
        return (
            <Breadcrumbs aria-label="breadcrumb" separator={<AngleRight />}>
                {breadcrumbs.map((item: any, index: number) =>
                    !item.active ? (
                        <Link key={index} to={item.path}>
                            {item.label}
                        </Link>
                    ) : (
                        <Typography key={index}>{item.label}</Typography>
                    )
                )}
            </Breadcrumbs>
        );
    };

    // With vapor 3 the title is allways to the left, so we don't need to adjust the layout @CC
    // // Reserves the space of action buttons to keep the title in the center, but remains hidden
    // const ActionButtonsPlaceholder = () => (
    //     <Box sx={{ visibility: "hidden", display: "flex", ml: 1 }}>
    //         <ActionButtons />
    //     </Box>
    // );

    return (
        <Title
            title={title}
            description={description}
            leftItems={showBackButton && <GoBackButton />}
            rightItems={<ActionButtons />}
            divider={devider}
            reduced={reduced}
            breadcrumbs={<Breadcurmbs />}
            size={size}
            titleItems={titleItems}
        />
    );
}
