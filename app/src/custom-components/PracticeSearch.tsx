import { FormControl, Input<PERSON>abel, TextField, ListItem } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { useEffect } from "react";
import useGetCustom from "../hooks/useGetCustom";
import { Practice } from "../interfaces/documents.interface";
import { AutocompleteProps } from "@mui/material";
import { debounce } from "lodash";
import { removeLinks } from "../utilities/utils";
import CustomAutocomplete from "./CustomAutocomplete";

export interface SearchArchiveProps {
    query: string;
    from: string;
}

export const useSearchArchive = ({
    from,
    query
}: SearchArchiveProps): {
    data: Practice[];
    error: any;
    hasLoaded: boolean;
    loading: boolean;
} => {
    const { data, doFetch, error, hasLoaded, loading } = useGetCustom("default/archive/search?noTemplateVars=true");

    const debouncedFetch = debounce((from: string, query: string) => {
        doFetch(true, {
            q: query,
            from: from
        });
    }, 1000);

    useEffect(() => {
        debouncedFetch(from, query);

        return () => {
            debouncedFetch.cancel();
        };
    }, [from, query]);

    return { data, error, hasLoaded, loading };
};

interface PracticeSearchProps {
    from?: string;
    query: string;
    value: Practice | string | null;
    onChange: AutocompleteProps<Practice | string, false, false, false>["onChange"];
    inputValue: string;
    onInputChange: AutocompleteProps<Practice | string, false, false, false>["onInputChange"];
    id?: string;
    width?: string | number;
}

export const PracticeSearch = ({ from, query, value, onChange, inputValue, onInputChange, id, width }: PracticeSearchProps) => {
    const { t } = useTranslation();

    const archiveSearchResponse = useSearchArchive({
        from: from || "",
        query: query
    });

    const options = archiveSearchResponse.hasLoaded ? archiveSearchResponse.data : [];

    return (
        <FormControl>
            <InputLabel>{t("Pratica")}</InputLabel>
            <CustomAutocomplete
                sx={{ width: width }}
                value={value}
                inputValue={removeLinks(inputValue)}
                onInputChange={onInputChange}
                id={id}
                noOptionsText={t("Nessuna pratica trovata")}
                onChange={onChange}
                loading={archiveSearchResponse.loading}
                options={options}
                getOptionLabel={(option: Practice) => removeLinks(option?.headerArchive || "")}
                renderInput={(params: any) => <TextField placeholder={t("Cerca pratica per codice, descrizione, nominativi, RG")} {...params} />}
                renderOption={(props: any, option: Practice) => (
                    <ListItem {...props} key={option.uniqueid}>
                        <div>{removeLinks(option.headerArchive)}</div>
                    </ListItem>
                )}
                isOptionEqualToValue={(option: any, value: any) => {
                    if (!option || !value) return false;
                    return option.uniqueid === value.uniqueid;
                }}
            />
        </FormControl>
    );
};
