import { <PERSON>box, FormControl, InputLabel, Text<PERSON>ield, Chip, Typography } from "@vapor/v3-components";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CustomAutocomplete from "./CustomAutocomplete";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const customRenderTags = (value: any[], getTagProps: any, ownerState: any) => {
    const displayLimit = 1; // We want to display only 1 tag explicitly.
    const getLabel = ownerState.getOptionLabel || ((option: any) => option.label); // Get label function from Autocomplete props

    if (!value || value.length === 0) {
        return [];
    }

    // Render the first tag
    const firstOption = value[0];
    const renderedElements = [
        <Chip
            {...getTagProps({ index: 0 })} // getTagProps for the first actual selected item
            key={getLabel(firstOption) + "-tag"} // Provide a key for the chip
            label={getLabel(firstOption)}
            size="small" // Match typical Autocomplete tag size
        />
    ];

    // If there are more items than we're displaying as chips
    if (value.length > displayLimit) {
        renderedElements.push(
            <Typography
                key="mui-autocomplete-more-tag"
                sx={{ marginLeft: '3px', alignSelf: 'center', fontSize: '0.8125rem' }} // Style for +N
            >
                +{value.length - displayLimit}
            </Typography>
        );
    }

    return renderedElements;
};

export const SelectMultiple = (props: any) => {
    const [selectedValues, setSelectedValue] = React.useState<any[]>([]);
    const [isOpen, setIsOpen] = useState(false);
    const { label, options, onChange, placeholder, selectedValues: values, name, width, style = {},target = "nome", keyValue = "id" } = props;

    React.useEffect(() => {
        if (values && Array.isArray(values)) setSelectedValue(values);
    }, [props.selectedValues]);

    const handleChange = (_event: React.SyntheticEvent, newValue: any[]) => {
        setSelectedValue(newValue);
        onChange(name, newValue);
    };

    const { t } = useTranslation();

    const selectedOptionsValues = React.useMemo(() => options.filter((v: any) => v.selected), [options]);

    return (
        <FormControl style={{ ...style, width: width ?? "270px" }} sx={{ width: "100%" }}>
            {label && <InputLabel id="ms-label">{label}</InputLabel>}
            <CustomAutocomplete
                multiple
                id={props.name}
                options={options}  
                disableCloseOnSelect
                value={selectedValues.length > 0 ? selectedValues : selectedOptionsValues}
                getOptionLabel={(option: any) => option[target]}
                isOptionEqualToValue={(option: any, value: any) => option[keyValue] === value[keyValue]}
                loadingText={t("Caricamento...")}
                noOptionsText={t("Nessuna opzione")}
                open={isOpen}
                onOpen={() => setIsOpen(true)}
                onClose={() => setIsOpen(false)}
                componentsProps={{
                    popupIndicator: {
                        title: isOpen ? t("Chiudi") : t("Apri")
                    }
                }}
                onChange={handleChange}
                renderTags={customRenderTags}
                renderOption={(props: any, option: any, state: any) => {
                    return (
                        <li key={option[keyValue]} {...props}>
                            <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={state.selected} />
                            {option?.[target]}
                        </li>
                    );
                }}
                renderInput={(params: any) => (
                    <TextField
                        {...params}
                        placeholder={selectedValues.length > 0 ? "" : (placeholder ?? t("Seleziona opzioni"))}
                    />
                    )}
                style={{
                    width: width ?? "270px"
                }}
                sx={{
                    // '.MuiAutocomplete-tag': {
                    // maxHeight: 28,
                    // },
                    '& .MuiAutocomplete-inputRoot': {
                     flexWrap: 'nowrap !important',
                    overflow: 'hidden',
                    },
                    '& .MuiAutocomplete-endAdornment': {
                        alignSelf: 'flex-start',
                        right: '0px !important',
                        position: 'absolute',
                    },
                    // height: 40, // Or any fixed height you want
                }}
            />
        </FormControl>
    );
};
