import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { CSSProperties, forwardRef, HTMLAttributes } from "react";
import { Card, CardContent } from "@vapor/react-material";
import { Typography } from "@vapor/react-material";

type Props = {
    item: any;
    content?: string;
} & HTMLAttributes<HTMLDivElement>;

export const SortableItem = ({ item, content, ...props }: Props) => {
    const {
        attributes,
        isDragging,
        listeners,
        setNodeRef,
        transform,
        transition,
    } = useSortable({
        id: item.id,
    });

    const styles = {
        transform: CSS.Transform.toString(transform),
        transition: transition || undefined,
    };

    return (
        <Item
            item={item}
            content={content}
            ref={setNodeRef}
            style={styles}
            isOpacityEnabled={isDragging}
            {...props}
            {...attributes}
            {...listeners}
        />
    );
};

type PropsItem = {
    item: any;
    content?: string;
    isOpacityEnabled?: boolean;
    isDragging?: boolean;
} & HTMLAttributes<HTMLDivElement>;
export const Item = forwardRef<HTMLDivElement, PropsItem>(
    ({ item, isOpacityEnabled, isDragging, style, ...props }, ref) => {
        const styles: CSSProperties = {
            opacity: isOpacityEnabled ? "0.4" : "1",
            cursor: isDragging ? "grabbing" : "grab",
            lineHeight: "0.5",
            transform: isDragging ? "scale(1.05)" : "scale(1)",
            ...style,
        };

        return (
            <div ref={ref} style={styles} {...props}>
                <Card
                    sx={{
                        // maxWidth: 300,
                        margin: 1,
                        height: 70,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <CardContent>
                        <Typography sx={{ color: "#04ABDD", pt: 0.7 }}>
                            {props.content === undefined
                                ? item.descrizione
                                : item[props.content]}
                        </Typography>
                    </CardContent>
                </Card>
            </div>
        );
    }
);
