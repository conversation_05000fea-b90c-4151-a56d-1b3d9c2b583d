import { useTheme } from "@vapor/react-material/styles";
import { CircularProgress, Box } from "@vapor/react-material";

const Spinner = ({ fullPage = true, height = "" }: any) => {
    const theme = useTheme();

    return (
        <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            padding={2}
            height={fullPage ? "100vh" : height}
            sx={{
                backgroundColor: theme.palette.background.default,
            }}
        >
            <CircularProgress />
        </Box>
    );
};

export default Spinner;
