import { Button, ButtonProps } from "@vapor/v3-components";

interface SpinnerButtonProps extends ButtonProps {
  label: string;
  isLoading?: boolean;
}

const SpinnerButton = ({
  label,
  isLoading = false,
  ...props
}: SpinnerButtonProps) => {
  return (
    <Button 
      disabled={isLoading || props.disabled}
      loading={isLoading}
      {...props}
    >
      {label}
    </Button>
  );
};

export default SpinnerButton;
