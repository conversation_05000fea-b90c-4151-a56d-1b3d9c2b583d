import React, { useState, useRef } from "react";
import {
    Button,
    ButtonGroup,
    <PERSON><PERSON>,
    <PERSON>row,
    Paper,
    ClickAwayListener,
    Stack,
} from "@mui/material";
import { MenuList, MenuItem, ListItemIcon } from "@vapor/react-material";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { Typography } from "@vapor/react-material";

type ButtonVariant = "text" | "outlined" | "contained";
type ButtonSize = "small" | "medium" | "large";

export interface SplitButtonOption {
    label: React.ReactNode;
    onClick: () => void;
    id?: string;
    icon?: any;
}

export interface MainButtonProps {
    label: React.ReactNode;
    onClick: () => void;
    icon?: any;
}

export interface SplitButtonProps {
    mainButton: MainButtonProps;
    options: SplitButtonOption[];
    variant?: ButtonVariant;
    size?: ButtonSize;
    enableDropdownButton?: boolean
}

export default function SplitButton(props: SplitButtonProps) {
    const { mainButton, options, variant = "text", size = "large", enableDropdownButton = false } = props;

    const [open, setOpen] = useState(false);
    const toggleRef = useRef<HTMLButtonElement>(null);

    const baseColor = variant === "contained" ? "inherit" : "#008BCC";

    const handleToggle = () => setOpen((prev) => !prev);

    const handleOptionClick = (_event: any, eventIndex: number) => {
        setOpen(false);
        options[eventIndex].onClick();
    };

    const handleClose = (event: Event) => {
        if (toggleRef.current?.contains(event.target as HTMLElement)) {
            return;
        }
        setOpen(false);
    };

    return (
        <Stack direction="row" spacing={1}>
            <ButtonGroup
                variant={variant}
                size={size}
                aria-label="split button"
            >
                <Button
                    onClick={mainButton.onClick}
                    size={size}
                    variant={variant}
                    startIcon={
                        mainButton.icon ? <mainButton.icon /> : undefined
                    }
                    sx={
                        variant === "outlined"
                            ? {
                                  color: baseColor,
                              }
                            : undefined
                    }
                >
                    <Typography
                        variant="buttonLarge"
                        sx={{
                            color: baseColor,
                            ...(variant === "outlined" && {
                                ".MuiButton-root:hover &": {
                                    color: "white",
                                },
                            }),
                        }}
                    >
                        {mainButton.label}
                    </Typography>
                </Button>

                <Button
                    ref={toggleRef}
                    disabled={!enableDropdownButton}
                    aria-haspopup="menu"
                    aria-expanded={open ? "true" : undefined}
                    size={size}
                    variant={variant}
                    sx={{
                        "&.MuiButtonBase-root": {
                            width: "20px !important",
                        },
                    }}
                    onClick={handleToggle}
                >
                    <ArrowDropDownIcon />
                </Button>
            </ButtonGroup>

            <Popper
                open={open}
                anchorEl={toggleRef.current}
                placement="bottom-end"
                transition
                disablePortal
                sx={{ zIndex: 1300 }}
            >
                {({ TransitionProps, placement }) => (
                    <Grow
                        {...TransitionProps}
                        style={{
                            transformOrigin:
                                placement === "bottom-end"
                                    ? "right top"
                                    : "right bottom",
                        }}
                    >
                        <Paper>
                            <ClickAwayListener onClickAway={handleClose}>
                                <MenuList autoFocusItem>
                                    {options.map((opt, idx) => (
                                        <MenuItem
                                            key={idx}
                                            onClick={(e) =>
                                                handleOptionClick(e as any, idx)
                                            }
                                        >
                                            {opt.label}
                                            {opt.icon && (
                                                <ListItemIcon>
                                                    <opt.icon sx={{ml: 2}}/>
                                                </ListItemIcon>
                                            )}
                                        </MenuItem>
                                    ))}
                                </MenuList>
                            </ClickAwayListener>
                        </Paper>
                    </Grow>
                )}
            </Popper>
        </Stack>
    );
}
