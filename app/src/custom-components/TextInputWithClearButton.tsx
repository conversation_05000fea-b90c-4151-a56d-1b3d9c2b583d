import { TextField, IconButton } from "@vapor/v3-components";
import { Xmark } from "@vapor/react-icons";

export interface ISelectedValue {
    nome: string;
    inputValue?: string;
    id?: string;
}

interface IProps {
    params: Record<string, any>;
    selectedValue: any;
    setSelectedValue: React.Dispatch<React.SetStateAction<any>>;
    label?: string;
    placeholder?: string;
    t?: (key: string) => string;
    isOnClickCustomState?: boolean;
    disableInput?: boolean;
}

export default function TextInputWithClearButton(props: IProps) {
    const {
        params,
        selectedValue,
        setSelectedValue,
        label,
        placeholder,
        isOnClickCustomState = false,
        disableInput = false,
    } = props;
    return (
        <TextField
            {...params}
            label={label}
            placeholder={placeholder || ""}
            onKeyDown={(e) => {
                if (disableInput) e.preventDefault(); // Prevent typing
            }}
            InputProps={{
                ...params.InputProps,
                endAdornment: (
                    <>
                        {params.InputProps.endAdornment}
                        {selectedValue && (
                            <IconButton
                                onClick={
                                    isOnClickCustomState
                                        ? setSelectedValue
                                        : () => setSelectedValue(null)
                                }
                                sx={{
                                    cursor: "pointer",
                                    ml: 3,
                                }}
                            >
                                <Xmark />
                            </IconButton>
                        )}
                    </>
                ),
            }}
            sx={{
                ...(disableInput && {
                    cursor: "not-allowed", 
                    "& .MuiInputBase-root": {
                        cursor: "not-allowed", 
                    },
                    "& input": {
                        cursor: "not-allowed", 
                        pointerEvents: "none", 
                    },
                }),
            }}
        />
    );
}
