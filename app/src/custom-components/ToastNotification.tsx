import { useEffect, memo } from "react";
import { createPortal } from "react-dom";
import {
    NotificationInline,
    AlertProps,
    AlertTitle,
} from "@vapor/v3-components";

type SetShowFunction = (value: boolean) => void;

interface ToastOptions {
    maxToasts: number;
    duration: number;
}

const defaultOptions: ToastOptions = {
    maxToasts: 6,
    duration: 5000,
};

const ToastManager = (() => {
    let toasts: SetShowFunction[] = [];

    return {
        register: (setShow: SetShowFunction) => {
            toasts.push(setShow);
        },
        unregister: (setShow: SetShowFunction) => {
            toasts = toasts.filter((t) => t !== setShow);
        },
        closeOldest: () => {
            const oldest = toasts.shift();
            oldest?.(false);
        },
        get count(): number {
            return toasts.length;
        },
    };
})();

let toastContainer: HTMLElement | null = null;

const getToastContainer = () => {
    if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.style.position = "fixed";
        toastContainer.style.top = "64px";
        toastContainer.style.right = "32px";
        toastContainer.style.zIndex = "9999";
        toastContainer.style.display = "flex";
        toastContainer.style.flexDirection = "column-reverse";
        toastContainer.style.gap = "8px";
        document.body.appendChild(toastContainer);
    }
    return toastContainer;
};

interface IToastNotification extends AlertProps {
    showNotification: boolean;
    setShowNotification: (value: boolean) => void;
    text: string;
    severity: AlertProps["severity"];
    action?: React.ReactNode;
    variant?: AlertProps["variant"];
    title?: AlertProps["title"];
    customButtons?: any;
}

const ToastNotification = memo(
    ({
        showNotification,
        setShowNotification,
        text,
        action,
        severity,
        variant = "outlined",
        title,
        customButtons = [],
    }: IToastNotification) => {
        useEffect(() => {
            let timer: NodeJS.Timeout;
            if (showNotification) {
                ToastManager.register(setShowNotification);
                if (ToastManager.count > defaultOptions.maxToasts) {
                    ToastManager.closeOldest();
                }
                timer = setTimeout(() => {
                    setShowNotification(false);
                }, defaultOptions.duration);
            }

            return () => {
                clearTimeout(timer);
                ToastManager.unregister(setShowNotification);
            };
        }, [showNotification, setShowNotification]);

        return createPortal(
            showNotification ? (
                <NotificationInline
                    sx={{
                        whiteSpace: "pre-line",
                        backgroundColor: "#fffaf0",
                        p: 2,
                    }}
                    action={action}
                    severity={severity}
                    variant={variant}
                    onClose={() => setShowNotification(false)}
                >
                    {title && <AlertTitle>{title}</AlertTitle>}
                    <div
                        style={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "8px",
                        }}
                    >
                        {text}
                        <div
                            style={{
                                display: "flex",
                                justifyContent: "flex-end",
                            }}
                        >
                            {customButtons.map((button: any, idx: number) => (
                                <div key={idx} style={{ marginLeft: "8px" }}>
                                    {button}
                                </div>
                            ))}
                        </div>
                    </div>
                </NotificationInline>
            ) : null,
            getToastContainer()
        );
    }
);

export default ToastNotification;
