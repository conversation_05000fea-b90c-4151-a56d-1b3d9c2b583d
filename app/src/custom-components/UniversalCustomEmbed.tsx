import { useHostPage, useAuth } from "@1f/react-sdk";
import { VaporPage } from "@vapor/react-custom";
import { Box } from "@vapor/react-material";
import { useEffect } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import "./main.css";

import { useConfigs } from "../store/ConfigStore";
import { useUser } from "../store/UserStore";
import { useApi } from "../store/ApiStore";
import { useLegacySwitch } from "../store/LegacySwitchStore";
import { DEFAULT_SUBDOMAIN } from "../utilities/constants";

const UniversalCustomEmbed = () => {
    const { dropToken } = useAuth();
    const { reset: resetConfig }: any = useConfigs();
    const { reset: resetUser }: any = useUser();
    const { api, sessionToken, reset: resetApi }: any = useApi();
    const { reset: resetlegacySwitch, legacySwitch }: any = useLegacySwitch();

    const navigate = useNavigate();
    const isTestLoginEnabled =
        import.meta.env.VITE_TEST_LOGIN_ENABLED === "true";
    const PHPSESSID = isTestLoginEnabled
        ? import.meta.env.VITE_TEST_TOKEN
        : sessionToken;
    const { "*": embeddedPath } = useParams();
    const location = useLocation();
    const usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;

    // const page = useHostPage(`${BASE_URN}://${usedSubdomain}/${embeddedPath}${location.search ? `${location.search}&` : '?'}UNIVERSAL=1&IS_LEGACY=${legacySwitch}&SESSION_TOKEN=${PHPSESSID}&XDEBUG_SESSION_START=PHPSTORM`);
    let pageUrl = `https://${usedSubdomain}.netlex.cloud/${embeddedPath}${
        location.search ? `${location.search}&` : "?"
    }UNIVERSAL=1&IS_LEGACY=${legacySwitch}&SESSION_TOKEN=${PHPSESSID}`;
    if (import.meta.env.DEV) {
        pageUrl = `${pageUrl}&XDEBUG_SESSION_START=PHPSTORM`;
    }

    const page = useHostPage(pageUrl);

    const handleLogout = () => {
        dropToken();
        resetUser();
        resetApi();
        resetConfig();
        resetlegacySwitch();
    };

    useEffect(() => {
        const messageHandler = (event: any) => {
            if (event.origin !== `https://${usedSubdomain}.netlex.cloud`) {
                return;
            }

            if (event.data && event.data.path) {
                if (event.data.path === "/logout") {
                    handleLogout();
                    return;
                }

                // Navigazione con sostituzione della history, nel caso in cui vado da legacy a legacy
                const { path, isReplaceable, queryString } = event.data;
                let newPath = path;
                if (queryString) {
                    const query = new URLSearchParams(queryString);

                    for (let pair of query.entries()) {
                        newPath = newPath.replace(`:${pair[0]}`, pair[1]);
                    }
                }

                navigate(newPath, { replace: isReplaceable });
            }
        };

        window.addEventListener("message", messageHandler);

        // Pulizia dell'evento listener
        return () => {
            window.removeEventListener("message", messageHandler);
        };
    }, []);

    return (
        <Box className="embedded-box">
            <VaporPage sx={{ position: "relative" }}>
                <VaporPage.Section>
                    <iframe
                        allowFullScreen
                        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads allow-popups-to-escape-sandbox"
                        src={page.url}
                        width="100%"
                        style={{
                            position: "absolute",
                            height: "100%",
                            border: "none",
                        }}
                        title="Embedded Content"
                    />
                </VaporPage.Section>
            </VaporPage>
        </Box>
    );
};

export default UniversalCustomEmbed;
