import Stack from "@mui/material/Stack";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import { Controller } from "react-hook-form";
import dayjs from "dayjs";

interface TimePickerProps {
    label: string;
    name: string;
    control: any;
    ampm?: boolean;
}

export const TimePickerUi = (props: TimePickerProps) => {
    const { name, label, control, ampm } = props;

    return (
        <Controller
            name={name}
            control={control}
            render={({ field: { onChange, value } }) => {
                return (
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <Stack spacing={2} sx={{ minWidth: 150 }}>
                            <TimePicker
                                label={label}
                                name={name}
                                value={dayjs(value)}
                                onChange={onChange}
                                ampm={ampm}
                            />
                        </Stack>
                    </LocalizationProvider>
                );
            }}
        />
    );
};
