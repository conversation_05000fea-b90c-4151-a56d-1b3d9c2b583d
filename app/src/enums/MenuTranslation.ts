export enum MENUITEM {
  CHECK_PDA = 'check_PDA',
  DASHBOARD = 'dashboard',
  ANAGRAFICHE = 'anagrafiche',
  ANAGRAFICHELIST = 'anagrafiche_list',
  PRACTICELIST = 'practices_list',
  PRACTICE = 'practices',
  AGENDA = 'agenda',
  BILLING = 'billing',
  MAILBOX = 'mailbox',
  ACTIVECICLE = 'activeCicle',
  PASSIVECICLE = 'passiveCicle',
  FONDOSOCCOMBENZA = 'fondosoccombenza',
  GENERAL_CALENDAR = 'general_cal',
  DAILYCALENDAR = 'daily_cal',
  LISTOFHEARINGS = 'list_of_hearings',
  LISTOFOVERDUEHEARINGS = 'list_of_overdue_hearings',
  TODOLIST = 'to_do_list',
  TIMESHEET = 'timesheet',
  MONTHLYREPORTTIMESHEET = 'monthly_report_timesheet',
  SOLICITED_AND_SUSPENDED = 'solicited_and_suspended',
  REPILOGUE = 'repilogue',
  INVOICES_NOTES_ADVANCES = 'invoices_notes_advances',
  BILLING_ITEMS = 'billing_items',
  SENDING_AND_RECEIVING = 'sending_and_receiving',
  MOVEMENTS = 'movements',
  ELECTRONIC_POST = 'electronic_post',
  PEC_NOTIFICATIONS = 'pec_notifications',
  SMART_MAILER = 'smart_mailer',
  PCT = 'PCT',
  STUDIO = 'studio',
  GROUPS = 'groups',
  GENERAL_SETTINGS = 'general_settings',
  EXTERNAL_USERS = 'external_users',
  USERS_STUDIO_DATA = 'users_studio_data',
  RECOVERY_CREDITS = 'recovery_credits',
  TABLES = 'tables',
  POLISWEB = 'polisweb',
  UTILITY = 'utility',
  SHOP = 'shop',
  NEWS = 'news',
  DOCUMENTS = 'documents',
  TSPAY = 'ts-pay',
  POSTEITALIANE = 'posteitaliane',
  ALLSYNCHRONIZATIONS = 'all_synchronizations',
  ALL_NON_IMPORT_SYNCHRONIZATIONS = 'all_non_import_synchronizations',
  AUTOMATIC_SYNCHRONIZATIONS = 'automatic_synchronizations',
  SYNCHRONIZED_DATA = 'synchronized_data',
  INCLUDED_POLISWEB = 'includedPolisWeb',
  EXCLUDED_ISSUES_LIST = 'excluded_issues_list',
  RGLISTFILTER = 'RGlistFilter',
  GOTODASHBOARD = 'goToDashboard',
  CONFIGURATIONS = 'configurations',
  CUSTOMIZATIONS = 'customization',
  LISTS_CUSTOMIZATION = 'lists_customization',
  PERSONAL_TEMPLATE = 'personal_template',
  OFFICESETTINGS = 'officesettings',
  ASSOCIATED_TEMPLATES_STUDIO = 'associated_templates_studio',
  REMOTE_SIGNATURE = 'remote_signature',
  LOGOS = 'logos',
  CERTIFICATE_LIST = 'certificate_list',
  EMAILTEMPLATE = 'email_templates',
  EMAIL = 'Email',
  TELEGRAM = 'telegram',
  USEFUL_MENAGMENT = 'useful_menagment',
  DELEGATIONS = 'delegations',
  PRACTICA = 'practica',
  REGISTRY_CATEGORIA = 'registryCategoria',
  DINAMIC_FIELDS = 'dinamic_fields',
  CALENDARS = 'calendars',
  CUSTOM_PRINT_TEMPLATE = 'custom_print_templates',
  FUNCTIONALITY_CUSTOMIZATION = 'functionality_customization',
  PRACTICECUSTOMIZATIONS = 'practiceSectionsCustomization',
  OTHER = 'other',
  LEGAL_UTILITIES = 'legal_utilities',
  SEPARATION_AMOUNTS = 'separation_of_amounts',
  TERM_CALCULATION = 'term_calculation',
  CALCULATION_INTERESTS = 'calculation_of_interests_on_arrears',
  LEGAL_CALCULATION = 'legal_interests_calculation',
  FIXED_RATE_CALCULATION = 'fixed_rate_interest_calculation',
  SPECIAL_SEARCHES = 'special_searches',
  DOCUMENTALE = 'documentale',
  CUSTOM_RATE = 'custom_rate',
  CLAUSES = 'clauses',
  TEMPLATES = 'templates',
  MACRO = 'macro',
  GESTIONE = 'gestione',
  INQUIRIES_BILANCE = 'inquiries_and_bilance',
  CERVED_CONSULTATION = 'CERVED_consultation',
  LIBRARY = 'library',
  LVENTURE_PDA = 'LVenture_PDA',
  FIXED_COSTS = 'fixed_costs',
  BANKS_CASHIERS = 'banks_and_cashiers',
  PAYMENT_METHOD = 'payment_methods',
  LETTERHEADS = 'letterheads',
  POLISWEB_RGINCLUSION = 'poliswebrginclusion',
  POLISWEB_RGEXCLUSION = 'poliswebrgexclusion',
  POLISWEB_RGFILTERS = 'poliswebrgfilters',
  EMAILACCOUNTS = 'emailaccounts',
  CERTIFICATES = 'certificates',
  REMOTE_SIGN = 'remotesign',
  NETLEXSETTINGS = 'netlexsettings',
  INTERNAL_USERS = 'internalUsers',
  CATEGORIES = 'categories'
}
