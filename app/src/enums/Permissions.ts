export enum CrudItems {
  DASHBOARD = 1,
  PRATICHE = 2,
  AGENDA = 3,
  FATTURAZIONE = 4,
  MAILBOX = 5,
  PDA = 6,
  POLISWEB = 7,
  PCT = 8,
  STUDIO = 9,
  UTILITA = 10,
  SHOP = 11,
  <PERSON><PERSON> = 12,
  CLIENTI = 13,
  TIMESHEET__MOSTRA_VALORI_ECONOMICI = 14,
  STUDIO__GRUPPI_UTENTI = 15,
  ACCESSO_COMPLETO_AI_DATI = 16,
  MOSTRA_TUTTI_GLI_AVVOCATI_UTENTI_NELLE_SELECT = 17,
  PRATICHE__AGENDA = 18,
  PRATICHE__FATTURAZIONE = 19,
  PRATICHE__EMAIL_E_NOTIFICHE_IN_PROPRIO = 20,
  PRATICHE__POLISWEB = 21,
  GESTIONE_ABBONAMENTO = 22,
  USA_WHITELIST_PER_SINCRONIZZAZIONE = 23,
  PRATICHE__STATISTICHE = 24,
  PRATICHE__DIVISIONE_DEGLI_UTILI = 25,
  PPT = 26,
  CONDIZIONI_GENERALI = 27,
  PRATICHE__DOCUMENTI = 28,
  UTILITA__DOCUMENTI = 29,
  PRATICHE__GESTIONE_DEL_RISCHIO = 30,
  FATTURAZIONE__VOCI_FATTURAZIONE = 31,
  PRATICHE__PRESTAZIONI = 32,
  FATTURAZIONE__MOVIMENTI = 33,
  DASHBOARD__FONDO_SOCCOMBENZA = 34,
  PRATICHE__MOVIMENTI = 35,
  TIMESHEET = 36,
  PRATICHE__TIMESHEET = 37,
  PRATICHE__ANTIRICICLAGGIO = 38,
  RECUPERO_CREDITI__IMPOSTAZIONI = 39,
  TIMESHEET__GESTIONE_TIMESHEET_DI_ALTRI_UTENTI = 40,
  POLISWEB__FILTRI_PER_LISTA_RG = 41,
  PRATICHE__DATI_LIQUIDAZIONE = 42,
  TABELLE__INSERIMENTO_RAPIDO = 43,
  UTILITA__LOG_PEC = 44,
  SUPPORTO__APERTURA_TICKET = 45,
  AGENDA_AGENDA = 52,
}

export enum CrudOperations {
  CREATE = "c",
  READ = "r",
  UPDATE = "u",
  DELETE = "d",
}
