export enum TabelleTitle {
    ATTIVITAUDIENZE = "Attività udienze",
    AUTHORITIES = "Autorità",
    BANKS = "Banche e casse",
    DOCUMENTCATEGORIES = "Categorie documenti",
    DEADLINECATEGORIES = "Categorie impegni",
    MACROCATEGORIES = "Categorie macro",
    MODELCATEGORIES = "Categorie modelli",
    CENTROPROFITTO = "Centro Profitto",
    CITIES = "Città",
    CONTACTANDADDRESS = "Contatti e indirizzi",
    LIQUIDATIONPACUMULATIVE = "Cumulativa Liquidazione PA",
    DESCRIPTIONS = "Descrizioni",
    INOUT = "In/out",
    INSTRUCTORS = "Istruttori",
    ITEMLIST = "Listino",
    TEMPLATES = "Modelli",
    PAYMENTTYPES = "Modi di pagamento",
    ADDITIONALNOTES = "Note aggiuntive",
    OBJECTS = "Oggetti",
    DOCUMENTOBJECT = "Oggetti documento",
    PUBLICPROSECUTORS = "PM",
    CRIMES = "Reati",
    LIQUIDATIONREFERENCE = "Riferimento liquidazione",
    PROCEDURALROLES = "Ruoli processuali",
    SITUAZIONE = "Situazione",
    SITUAZIONECONTABILE = "Situazione contabile",
    DOCUMENTSTATUS = "Stati documento",
    DEADLINESTATUS = "Stati impegni",
    FILESSTATUS = "Stati pratica",
    SEDI = "Sedi",
    SECTIONAL = "Sezionali fatture",
    EXPENDITURES = "Spese fisse",
    SPIEGAZIONI = "Spiegazioni movimenti",
    LIQUIDATIONPASTATUS = "Stato liquidazione PA",
    LIQUIDATIONPAREGSTATUS = "Stato imposta di registro",
    TAGS = "Tag",
    ATTIVITA = "Timesheet standard",
    DEADLINESTANDARD = "Impegni standard",
    LIQUIDATIONPADEEDTYPE = "Tipo atto liquidazione PA",
    DEADLINESTYPES = "Tipologie impegno",
    FILESTYPES = "Tipologie pratica",
    ANTIRECREGISTER = "Registro unico antiriciclaggio",
    UNITMEASURE = "Unità di misura",
    CURRENCIES = "Valute",
}
