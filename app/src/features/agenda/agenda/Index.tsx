import Agenda from "./AgendaList";
import CreateUdienza from "../generalCalendar/sections/CreateUdienza";
import moment from "moment";

export const DEFAULT_LIST_PARAMS = {
  noTemplateVars: true,
  page: 0,
  pageSize: 10,
  sortColumn: "listaclienti",
  sortOrder: "asc",
  startDateSearch: moment().format("DD/MM/YYYY"),
  endDateSearch: "",
  searchCity: "-1",
  searchInstructors: "-1",
  searchAuthorities: "-1",
  searchLawyers: "-1",
  searchReferents: "-1",
  searchEvase: "0",
  ntbprocessed: "0",
  searchActivity: "",
  isPostponed: "-1",
  poliswebFilter: "",
};

export const agendaLegale = () => [
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/agenda/agenda",
      element: <Agenda />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/archiveagenda/agenda/create/:id",
      element: <CreateUdienza />,
    },
  },
];
