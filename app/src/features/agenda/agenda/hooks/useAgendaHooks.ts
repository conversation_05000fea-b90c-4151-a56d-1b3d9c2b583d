import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { getAgendaGrids } from "../../../../utilities/agenda/gridColumn";
import { useTranslation } from "@1f/react-sdk";

export const useAgendaHooks = () => {
    const { t } = useTranslation();
    const [columns, setColumns] = useState<any[]>([]);
    const [searchCities, setSearchCities] = useState<any[]>([]);
    const [searchInstructors, setSearchInstructors] = useState<any[]>([]);
    const [loggedUser, setLoggedUser] = useState<any>({});
    const [fileStatus, setFileStatus] = useState<any[]>([]);
    const [searchAuthorities, setSearchAuthorities] = useState<any[]>([]);
    const [searchLawyers, setSearchLawyers] = useState<any[]>([]);
    const [searchReferents, setSearchReferents] = useState<any[]>([]);
    const [items, setItems] = useState<any[]>([]);
    const [childTypes, setChildTypes] = useState<any>();
    const [evase, setEvase] = useState<any>();
    const agendaRequest = useGetCustom("agenda/agenda");

    useEffect(() => {
        if (
            !columns.length ||
            !searchInstructors.length ||
            !searchCities.length ||
            searchLawyers.length
        )
            initAgenda();
    }, []);

    async function initAgenda() {
        try {
            const response: any = await agendaRequest.doFetch();
            setSearchCities(response.data.searchCities);
            setSearchInstructors(response.data.searchInstructors);
            setSearchAuthorities(response.data.searchAuthorities);
            setSearchLawyers(response.data.searchLawyers);
            setSearchReferents(response.data.searchReferents);
            setChildTypes(response.data.childType);
            setItems(response.data.items);
            setEvase(response.data.evase);
            setFileStatus(response.data.fileStatus);
            setLoggedUser(response.data.loggedUser);
            const finalColumns: any = await getAgendaGrids(response.data, t);
            setColumns(finalColumns);
        } catch (error) {
            console.log("Agenda error", error);
            return;
        }
    }

    return {
        t,
        columns,
        searchCities,
        searchInstructors,
        searchAuthorities,
        searchLawyers,
        searchReferents,
        items,
        childTypes,
        evase,
        fileStatus,
        loggedUser,
        fetchAgain: initAgenda,
    };
};
