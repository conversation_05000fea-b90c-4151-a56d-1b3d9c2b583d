import {
  Box,
  TextField,
  Button,
  FormControl,
  FormControlLabel,
  MenuItem,
  Select,
  Checkbox,
} from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import { processDateValue } from "../../../../helpers/dateHelper";

export const Filters = (props: any) => {
  const { t } = useTranslation();

  const {
    params: defaultParams,
    onChangeFunctions,
    date,
    searchCities,
    searchInstructors,
    searchAuthorities,
    searchLawyers,
    searchReferents,
  } = props;

  const {
    onChangeInput,
    onClickReset,
    handleSearchActivita,
    onDateChange,
    onChangeCheckbox,
  } = onChangeFunctions;

  return (
    <>
      <Box display="flex" alignItems="end" gap={1}>
        <div style={{ width: "20%" }}>
          <DatePicker
            label={`${t("Dal")}:`}
            value={processDateValue(date.startDateSearch)}
            onChange={(date: Date | null) => {
                if (date) onDateChange("startDateSearch", date);
            }}
          />
        </div>

        <div style={{ width: "20%" }}>
          <DatePicker
            label={`${t("Al")}:`}
            value={processDateValue(date.endDateSearch)}
            onChange={(date: Date | null) => {
                if (date) onDateChange("endDateSearch", date);
            }}
          />
        </div>
        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchCity}
            label="Tutte le città"
            onChange={onChangeInput}
            name="searchCity"
          >
            <MenuItem value={-1}>{t("Tutte le città")}</MenuItem>

            {searchCities?.map((searchCity: any) => (
              <MenuItem key={searchCity.id} value={searchCity.id}>
                {searchCity.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchInstructors}
            label={t("Tutti i giudici")}
            onChange={onChangeInput}
            name="searchInstructors"
          >
            <MenuItem value={-1}>{t("Tutti i giudici")}</MenuItem>
            {searchInstructors?.map((searchInstructor: any) => (
              <MenuItem key={searchInstructor.id} value={searchInstructor.id}>
                {searchInstructor.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchAuthorities}
            label={t("Tutte le autorità")}
            onChange={onChangeInput}
            name="searchAuthorities"
          >
            <MenuItem value={-1}>{t("Tutte le autorità")}</MenuItem>
            {searchAuthorities?.map((searchAuthority: any) => (
              <MenuItem key={searchAuthority.id} value={searchAuthority.id}>
                {searchAuthority.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchLawyers}
            label={t("Tutti gli avvocati")}
            onChange={onChangeInput}
            name="searchLawyers"
          >
            <MenuItem value={-1}>{t("Tutti gli avvocati")}</MenuItem>
            {searchLawyers?.map((searchLawyer: any) => (
              <MenuItem key={searchLawyer.id} value={searchLawyer.id}>
                {searchLawyer.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
      <Box display="flex" alignItems="end" gap={2} sx={{ pt: 1 }}>
        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchReferents}
            label="Tutti i giudici"
            onChange={onChangeInput}
            name="searchReferents"
          >
            <MenuItem value={-1}>{t("Tutti i referenti")}</MenuItem>
            {searchReferents?.map((searchReferent: any) => (
              <MenuItem key={searchReferent.id} value={searchReferent.id}>
                {searchReferent.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchEvase}
            label="Tutti gli stati"
            onChange={onChangeInput}
            name="searchEvase"
          >
            <MenuItem value={0}>{t("Tutti gli stati")}</MenuItem>
            <MenuItem value={1}>{t("Evase")}</MenuItem>
            <MenuItem value={2}>{t("Non evase")}</MenuItem>
          </Select>
        </FormControl>
        {defaultParams.searchEvase === 2 && (
          <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
            <Select
              labelId="select-label"
              value={defaultParams.ntbprocessed}
              label="Tutti gli stati"
              onChange={onChangeInput}
              name="ntbprocessed"
            >
              <MenuItem value={0}>{t("Tutti gli stati")}</MenuItem>
              <MenuItem value={1}>{t("Da evadere")}</MenuItem>
              <MenuItem value={2}>{t("Da non evadere")}</MenuItem>
            </Select>
          </FormControl>
        )}

        <TextField
          variant="outlined"
          value={defaultParams.searchActivity}
          placeholder="Attività:"
          name="searchActivity"
          sx={{ width: 1 / 4 }}
          onChange={onChangeInput}
          onKeyDown={handleSearchActivita}
        />
        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.isPostponed}
            label="Tutte le udienze"
            onChange={onChangeInput}
            name="isPostponed"
          >
            <MenuItem value={-1}>{t("Tutte le udienze")}</MenuItem>
            <MenuItem value={1}>{t("Udienze rinviate")}</MenuItem>
            <MenuItem value={2}>{t("Udienze non rinviate")}</MenuItem>
          </Select>
        </FormControl>
        <FormControlLabel
          sx={{ width: 1 / 8 }}
          control={
            <Checkbox
              onChange={onChangeCheckbox}
              color="primary"
              name="poliswebFilter"
              checked={!!defaultParams.poliswebFilter}
            />
          }
          label="Polisweb"
        />
        <Button variant="contained" color="primary" onClick={onClickReset}>
          {t("Annulla ricerca")}
        </Button>
      </Box>
    </>
  );
};
