import React, { useState, useEffect } from "react";
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    DialogActions,
    DialogContentText,
    Divider,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Radio,
} from "@vapor/react-material";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import useGetCustom from "../../../../hooks/useGetCustom";
import { updateParams } from "../utils";

const StampaTemplateModal = (props: any) => {
    const { openModal, setOpenModal, params, date } = props;
    const category = "4";
    const [printFilters, setPrintFilters] = useState<any[]>([]);
    const { t } = useTranslation();
    const [selectedValue, setSelectedValue] = React.useState("0");    


    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSelectedValue(event.target.value);
    };

    const printFilter = usePostCustom(
        "prints/filter-templates?noTemplateVars=true"
    );

    params.category = category;
    params.docid = selectedValue;

    const confermaRequest = useGetCustom(
        "agenda/print-model-bysearch",
        updateParams(params, date),  
        null,
        true
    );
    const printButton = async () => {
        const response: any = await printFilter.doFetch(true, {
            category: category,
        });
        setPrintFilters(response.data);
       
    };

    const confermaButton = async () => {
        await confermaRequest.doFetch(true);
        setOpenModal(!openModal);

       
        const response: any = await confermaRequest.doFetch(
            updateParams(params, date, true)
        );
        const blob = new Blob([response.data], { type: "application/rtf" });
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = "Stampa.rtf";
        document.body.appendChild(link);
        link.click();

        if (link.parentNode) link.parentNode.removeChild(link);
   
    };

    useEffect(() => {
        printButton();
    }, [category]);

    return (
        <>
            <Dialog
                open={openModal}
                onClose={() => setOpenModal(!openModal)}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle>
                    {t("Scegli il template da utilizzare")}

                    <Box display="flex" alignItems="right">
                        <IconButton
                            color="primary"
                            onClick={() => setOpenModal(!openModal)}
                        >
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        {" "}
                        <Box display="flex" gap={1} sx={{ mt: 2 }}>
                            <TableContainer component={Paper}>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell
                                                style={{ textAlign: "center" }}
                                            ></TableCell>
                                            <TableCell
                                                style={{ textAlign: "center" }}
                                            >
                                                {t("Nome")}
                                            </TableCell>
                                            <TableCell
                                                style={{ textAlign: "center" }}
                                            >
                                                {t("Descrizione")}
                                            </TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {printFilters.length > 0 ? (
                                            printFilters?.map(
                                                (
                                                    filter: any,
                                                    _index: number
                                                ) => {
                                                    return (
                                                        <TableRow
                                                            key={filter.id}
                                                        >
                                                            <TableCell
                                                                width={"20%"}
                                                            >
                                                                <Radio
                                                                    checked={
                                                                        selectedValue ===
                                                                        filter.id
                                                                    }
                                                                    onChange={
                                                                        handleChange
                                                                    }
                                                                    value={
                                                                        filter.id
                                                                    }
                                                                    name="radio-buttons"
                                                                />
                                                            </TableCell>
                                                            <TableCell
                                                                width={"40%"}
                                                            >
                                                                {
                                                                    filter.filename
                                                                }
                                                            </TableCell>
                                                            <TableCell
                                                                width={"40%"}
                                                            >
                                                                {filter.title}
                                                            </TableCell>
                                                        </TableRow>
                                                    );
                                                }
                                            )
                                        ) : (
                                            <TableRow>
                                                <TableCell colSpan={3}>
                                                    {t(
                                                        "Non ci sono template caricati per questa sezione"
                                                    )}
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Box>
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button
                        variant="outlined"
                        onClick={() => setOpenModal(!openModal)}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" onClick={confermaButton}>
                        {t("Conferma")}
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default StampaTemplateModal;
