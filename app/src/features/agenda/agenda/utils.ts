import moment from "moment";

export const updateParams = (params: any, date: any, isDownload = false) => {
  const { startagendaSearchValue, endagendaSearchValue, ...restParams } =
    params;


  if (date.startDateSearch) {
    // Handle both Date objects and formatted strings
    if (date.startDateSearch instanceof Date) {
      restParams.startDateSearch = moment(date.startDateSearch).format("DD/MM/YYYY");
    } else {
      restParams.startDateSearch = moment(date.startDateSearch, "DD/MM/YYYY").format(
        "DD/MM/YYYY"
      );
    }
  } else {
    restParams.startDateSearch = "";
  }


  if (date.endDateSearch && params.endDateSearch) {
    // Handle both Date objects and formatted strings
    if (date.endDateSearch instanceof Date) {
      restParams.endDateSearch = moment(date.endDateSearch).format("DD/MM/YYYY");
    } else {
      restParams.endDateSearch = moment(date.endDateSearch, "DD/MM/YYYY").format("DD/MM/YYYY");
    }
  } else {
    restParams.endDateSearch = "";
  }
  if (params.poliswebFilter) {
    restParams.poliswebFilter = "on";
  } else {
    delete restParams.poliswebFilter;
  }

  if (isDownload) {
    delete restParams.category;
    delete restParams.page;
    delete restParams.pageSize;
    delete restParams.sortOrder;
    delete restParams.sortColumn;
  }

  return restParams;
};
