import VaporPage from "@vapor/react-custom/VaporPage";
import { useRef, useState, useEffect } from "react";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { useAgendaDelayedHook } from "./hooks/useAgendaDelayedHook";
import useGetCustom from "../../../hooks/useGetCustom";
import { DEFAULT_LIST_PARAMS } from "./Index";
import { Filters } from "./sections/Filters";
import { useNavigate } from "react-router-dom";
import { debounce } from "lodash";
import { AlertBox } from "../../../custom-components/AlertBox";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import {
    GridPaginationModel,
    GridCallbackDetails,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import moment from "moment";
const updateParams = (params: any, _date: any) => {
    const { startagendaSearchValue, endagendaSearchValue, ...restParams } =
        params;

    return restParams;
};

export default function AgendaIndex() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [data, setData] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);

    const [defaultParams, setDefaultParams] = useState(DEFAULT_LIST_PARAMS);
    const [date, setDate] = useState({
        startDateSearch: new Date(),
        endDateSearch: null,
    });
    const resetClicked = useRef(false);

    const agendaDelayedListRequest = useGetCustom(
        "agendadelayed/list",
        updateParams(defaultParams, date)
    );

    const exportPdf = useGetCustom("agendadelayed/printList", {}, null, true);

    const defaultagendaDelayedListRequest = useGetCustom(
        "agendadelayed/list",
        updateParams(DEFAULT_LIST_PARAMS, date)
    );

    const onPageChange = (_: any, page: number) => {
        setDefaultParams({
            ...defaultParams,
            page,
        });
    };

    const onDateChange = (name: string, value: Date) => {
        setDate((prevValue: any) => ({ ...prevValue, [name]: value }));
    };

    const onSortChange = (column: any, direction: any) => {
        setDefaultParams({
            ...defaultParams,
            sortColumn: column,
            sortOrder: direction,
        });
    };

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? defaultagendaDelayedListRequest.doFetch(true)
            : agendaDelayedListRequest.doFetch(true));

        if (reset) {
            resetClicked.current = false;
        }

        const { currentPage, totalRows } = response.data;

        setData(currentPage);
        setTotalRows(totalRows);
    };

    const handleRowSelection = (
        rowSelectionModel: GridRowSelectionModel,
        details: GridCallbackDetails<any>
    ) => {
        console.log("details", details);
        console.log("rowSelectionModel", rowSelectionModel);
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onClickCallback = (fileUniqueid: any) => {
        navigate(`/legacy/archiveagenda/agenda?fileUniqueid=${fileUniqueid}`);
    };

    useEffect(() => {
        const debouncedSearch = debounce(() => {
            startSearchList();
        }, 500);
        defaultParams.searchActivity && debouncedSearch();
        return () => {
            debouncedSearch.cancel();
        };
    }, [defaultParams.searchActivity]);

    useEffect(() => {
        if (!resetClicked.current) {
            startSearchList();
        }
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        defaultParams.searchEvase,
        defaultParams.searchAuthorities,
        defaultParams.searchReferents,
        defaultParams.searchLawyers,
        defaultParams.searchNonevadere,
        defaultParams.searchActivity,
    ]);

    const {
        columns,
        searchInstructors,
        searchAuthorities,
        searchLawyers,
        searchReferents,
    } = useAgendaDelayedHook();

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="agendadelayed"
                columns={columns}
                data={data}
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={
                    agendaDelayedListRequest.loading ||
                    defaultagendaDelayedListRequest.loading
                }
                query={defaultParams}
                setQuery={setDefaultParams}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={onClickCallback}
                onRowSelectionModelChange={handleRowSelection}
                onClickKey="fileUniqueid"
            />
        );
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;

        const newParams = {
            ...defaultParams,
            [name]: value,
        };

        setDefaultParams(newParams);
    };

    const onChangeCheckbox = (e: any) => {
        const { name, checked } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: checked || "",
        });
    };

    const onClickReset = async () => {
        resetClicked.current = true;
        setDefaultParams(DEFAULT_LIST_PARAMS);
        startSearchList(true);
        setDate({
            startDateSearch: new Date(),
            endDateSearch: null,
        });
    };

    const handleSearch = () => {
        startSearchList();
    };

    const onChangeFunctions = {
        onChangeCheckbox,
        onChangeInput,
        onDateChange,
        onClickReset,
        onSortChange,
        onPageChange,
        handleSearch,
    };

    const printInPdf = async () => {
        const response: any = await exportPdf.doFetch(true);

        const blob = new Blob([response.data], { type: "application/pdf" });
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = `Lista_udienze_ritardo_${moment().format(
            "DD_MM_YYYY"
        )}.pdf`;

        document.body.appendChild(link);
        link.click();
        if (link.parentNode) link.parentNode.removeChild(link);
    };

    return (
        <VaporPage>
            <AlertBox>
                {t(
                    "Il seguente elenco contiene le udienze passate che non hanno un rinvio e che sono collegate a pratiche aperte il cui stato è marcato come 'rinviabile'."
                )}
            </AlertBox>
            <PageTitle
                title="UDIENZE IN RITARDO"
                showBackButton={false}
                actionButtons={[{ label: t("Stampa"), onclick: printInPdf }]}
            />
            <VaporPage.Section>
                <Filters
                    params={defaultParams}
                    onChangeFunctions={onChangeFunctions}
                    date={date}
                    searchInstructors={searchInstructors}
                    searchAuthorities={searchAuthorities}
                    searchLawyers={searchLawyers}
                    searchReferents={searchReferents}
                />
            </VaporPage.Section>

            <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
        </VaporPage>
    );
}
