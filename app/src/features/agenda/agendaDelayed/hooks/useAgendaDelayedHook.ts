import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { getAgendaGrids } from "../../../../utilities/agenda/gridColumn";
import { useTranslation } from "@1f/react-sdk";

export const useAgendaDelayedHook = () => {
    const { t } = useTranslation();
    const [columns, setColumns] = useState<any[]>([]);
    const [searchInstructors, setSearchInstructors] = useState<any[]>([]);
    const [searchAuthorities, setSearchAuthorities] = useState<any[]>([]);
    const [searchLawyers, setSearchLawyers] = useState<any[]>([]);
    const [searchReferents, setSearchReferents] = useState<any[]>([]);
    const agendaDelayedRequest = useGetCustom("agendadelayed/agendadelayed");

    useEffect(() => {
        if (
            !columns.length ||
            !searchInstructors.length ||
            searchLawyers.length
        )
            initAgenda();
    }, []);

    async function initAgenda() {
        try {
            const response: any = await agendaDelayedRequest.doFetch();

            setSearchInstructors(response.data.searchInstructors);
            setSearchAuthorities(response.data.searchAuthorities);
            setSearchLawyers(response.data.searchLawyers);
            setSearchReferents(response.data.searchReferents);
            const finalColumns: any = await getAgendaGrids(response.data, t);
            setColumns(finalColumns);
        } catch (error) {
            console.log("Agenda error", error);
            return;
        }
    }

    return {
        t,
        columns,
        searchInstructors,
        searchAuthorities,
        searchLawyers,
        searchReferents,

        fetchAgain: initAgenda,
    };
};
