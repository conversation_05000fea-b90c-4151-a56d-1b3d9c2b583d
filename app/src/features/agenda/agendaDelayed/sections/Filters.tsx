import {
  Box,
  TextField,
  Button,
  FormControl,
  MenuItem,
  Select,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

export const Filters = (props: any) => {
  const { t } = useTranslation();

  const {
    params: defaultParams,
    onChangeFunctions,
    searchAuthorities,
    searchLawyers,
    searchReferents,
  } = props;

  const { onChangeInput, onClickReset, handleSearchActivita, handleSearch } =
    onChangeFunctions;

  return (
    <>
      <Box display="flex" alignItems="end" gap={2}>
        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchAuthorities}
            label={t("Tutte le autorità")}
            onChange={onChangeInput}
            name="searchAuthorities"
          >
            <MenuItem value={-1}>{t("Tutte le autorità")}</MenuItem>

            {searchAuthorities?.map((searchAuthority: any) => (
              <MenuItem key={searchAuthority.id} value={searchAuthority.id}>
                {searchAuthority.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchLawyers}
            label={t("Tutti gli avvocati")}
            onChange={onChangeInput}
            name="searchLawyers"
          >
            <MenuItem value={-1}>{t("Tutti gli avvocati")}</MenuItem>

            {searchLawyers?.map((searchLawyer: any) => (
              <MenuItem key={searchLawyer.id} value={searchLawyer.id}>
                {searchLawyer.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchReferents}
            label="Tutti i giudici"
            onChange={onChangeInput}
            name="searchReferents"
          >
            <MenuItem value={-1}>{t("Tutti i referenti")}</MenuItem>

            {searchReferents?.map((searchReferent: any) => (
              <MenuItem key={searchReferent.id} value={searchReferent.id}>
                {searchReferent.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchEvase}
            label="Tutti gli stati"
            onChange={onChangeInput}
            name="searchEvase"
          >
            <MenuItem value={0}>{t("Tutti gli stati")}</MenuItem>
            <MenuItem value={1}>{t("Evase")}</MenuItem>
            <MenuItem value={2}>{t("Non evase")}</MenuItem>
          </Select>
        </FormControl>

        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <Select
            labelId="select-label"
            value={defaultParams.searchNonevadere}
            onChange={onChangeInput}
            name="searchNonevadere"
          >
            <MenuItem value={-1}>{t("Mostra da evadere/non evadere")}</MenuItem>
            <MenuItem value={0}>{t("Da evadere")}</MenuItem>
            <MenuItem value={1}>{t("Da non evadere")}</MenuItem>
          </Select>
        </FormControl>

        <TextField
          variant="outlined"
          value={defaultParams.searchActivity}
          placeholder="Attività:"
          name="searchActivity"
          sx={{ width: 1 / 4 }}
          onChange={onChangeInput}
          onKeyDown={handleSearchActivita}
        />
      </Box>
      <Box display="flex" alignItems="end" gap={2} sx={{ pt: 1 }}>
        <Button variant="contained" color="primary" onClick={handleSearch}>
          {t("Cerca")}
        </Button>
        <Button variant="contained" color="primary" onClick={onClickReset}>
          {t("Mostra tutte")}
        </Button>
      </Box>
    </>
  );
};
