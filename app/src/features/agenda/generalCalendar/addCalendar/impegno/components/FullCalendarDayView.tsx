import {
    Box,
    Grid,
    IconButton,
    Stack,
    Typography,
    ExtendedAvatar,
    FormControl,
    Select,
    MenuItem,
    DatePicker,
    AdapterDateFns,
    LocalizationProvider,
} from "@vapor/v3-components";
import FullCalendar from "@fullcalendar/react";
import timeGridPlugin from "@fullcalendar/timegrid";
import itLocale from "@fullcalendar/core/locales/it";
import dayGridPlugin from "@fullcalendar/daygrid"; // a plugin!
import { useState, useRef } from "react";
import { CalendarToday } from "@mui/icons-material";
import ArrowLeftIcon from "@mui/icons-material/ArrowLeft";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowRightFromLine } from "@fortawesome/pro-regular-svg-icons";
import "./miniCalendar.css";
import { it } from "date-fns/locale";

const users = [
    { id: 1, name: "<PERSON>" },
    { id: 2, name: "<PERSON>" },
    { id: 3, name: "<PERSON>" },
];

interface IProps {
    setCalendarOpen: (arg: any) => void;
}

export default function FullCalendarDayView(props: IProps) {
    const { setCalendarOpen } = props;
    const [selectedDate, setSelectedDate] = useState(new Date());
    const calendarRef = useRef<FullCalendar>(null);
    const [selectedUser, _setSelectedUser] = useState("Mario Rossi");
    const [openDatePicker, setOpenDatePicker] = useState(false);

    // Navigation functions
    const handlePrevDay = () => {
        const newDate = new Date(selectedDate);
        newDate.setDate(newDate.getDate() - 1);
        setSelectedDate(newDate);

        // Update FullCalendar view
        if (calendarRef.current) {
            const calendarApi = calendarRef.current.getApi();
            calendarApi.gotoDate(newDate);
        }
    };

    const handleNextDay = () => {
        const newDate = new Date(selectedDate);
        newDate.setDate(newDate.getDate() + 1);
        setSelectedDate(newDate);

        // Update FullCalendar view
        if (calendarRef.current) {
            const calendarApi = calendarRef.current.getApi();
            calendarApi.gotoDate(newDate);
        }
    };

    const formatDate = (date: any) => {
        return new Intl.DateTimeFormat("it-IT", {
            weekday: "short",
            day: "numeric",
            month: "short",
            year: "numeric",
        }).format(date);
    };

    const handleDatePickerChange = (date: any) => {
        if (!date || !calendarRef.current) return;

        // update state
        setSelectedDate(date);

        // jump FullCalendar
        const calApi = calendarRef.current.getApi();
        calApi.gotoDate(date);

        // close the picker
        setOpenDatePicker(false);
    };

    return (
        <Grid item xs={2} minWidth={400}>
            <Box
                className="miniCalendarWrapper "
                sx={{
                    pl: 2,
                    height: "100%",
                }}
            >
                {/* Custom Header */}
                <Box sx={{ mb: 2 }}>
                    {/* First Row: Navigation and Date */}
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            mb: 1.5,
                        }}
                    >
                        <Stack
                            direction="row"
                            alignItems="center"
                            spacing={0.5}
                        >
                            <IconButton onClick={handlePrevDay}>
                                <ArrowLeftIcon />
                            </IconButton>

                            <IconButton onClick={handleNextDay}>
                                <ArrowRightIcon />
                            </IconButton>

                            <Typography
                                variant="titleSmall"
                                color="contentInformative"
                            >
                                {formatDate(selectedDate)}
                            </Typography>
                        </Stack>

                        <IconButton onClick={() => setOpenDatePicker(true)}>
                            <CalendarToday />
                        </IconButton>
                        {openDatePicker && (
                            <LocalizationProvider
                                dateAdapter={AdapterDateFns}
                                adapterLocale={it}
                            >
                                <DatePicker
                                    open={openDatePicker}
                                    onClose={() => setOpenDatePicker(false)}
                                    onChange={handleDatePickerChange}
                                    format="dd/MM/yyyy"
                                    slotProps={{
                                        textField: {
                                            error: false,
                                            sx: {
                                                visibility: "hidden",
                                                width: 0,
                                            },
                                        },
                                        day: {
                                            sx: {
                                                "&.MuiPickersDay-today": {
                                                    backgroundColor:
                                                        "#005075 !important",
                                                    color: "#ffffff !important",
                                                    fontWeight:
                                                        "bold !important",
                                                    borderRadius:
                                                        "50% !important",
                                                    border: "none !important",
                                                },
                                                "&.MuiPickersDay-today:hover": {
                                                    backgroundColor: "#005075",
                                                    color: "#ffffff",
                                                },
                                                "&.Mui-selected": {
                                                    backgroundColor:
                                                        "#005075 !important",
                                                    color: "#ffffff !important",
                                                    fontWeight:
                                                        "bold !important",
                                                    borderRadius:
                                                        "50% !important",
                                                },
                                                "&.Mui-selected:hover": {
                                                    backgroundColor: "#005075",
                                                    color: "#ffffff",
                                                },
                                            },
                                        },
                                    }}
                                />
                            </LocalizationProvider>
                        )}
                        <IconButton onClick={() => setCalendarOpen(false)}>
                            <FontAwesomeIcon icon={faArrowRightFromLine} />
                        </IconButton>
                    </Box>

                    {/* Second Row: User Selection */}
                    <Box sx={{ mb: 1.5 }} display="flex">
                        <ExtendedAvatar sx={{ mr: 1 }} size="medium" tenant />
                        <FormControl fullWidth size="small">
                            <Select
                                value={selectedUser}
                                displayEmpty
                                sx={{
                                    "& .MuiSelect-select": {
                                        display: "flex",
                                        alignItems: "center",
                                        py: 1,
                                    },
                                    "& .MuiOutlinedInput-notchedOutline": {
                                        borderColor: "divider",
                                    },
                                }}
                            >
                                {users.map((user) => (
                                    <MenuItem key={user.id} value={user.name}>
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                gap: 1.5,
                                            }}
                                        >
                                            <Typography variant="body2">
                                                {user.name}
                                            </Typography>
                                        </Box>
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Box>
                </Box>

                {/* FullCalendar */}
                <FullCalendar
                    ref={calendarRef}
                    plugins={[timeGridPlugin, dayGridPlugin]}
                    headerToolbar={false}
                    allDaySlot={false}
                    dayHeaders={false}
                    initialView="timeGridDay"
                    initialDate={selectedDate}
                    height={600}
                    locales={[itLocale]}
                    locale="it"
                    slotMinTime="08:00:00"
                    slotMaxTime="20:00:00"
                    slotDuration="01:00:00"
                    slotLabelFormat={{
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: false,
                    }}
                />
            </Box>
        </Grid>
    );
}
