/* 1) Override the height of each time-slot row */
.miniCalendarWrapper .fc .fc-timegrid-slots td div {
  height: 50px !important;
  border-bottom: none !important;
}

/* 2) Base slot cell: remove default bottom border */
.miniCalendarWrapper .fc .fc-timegrid-slot {
  position: relative;
  border-bottom: none;
}

/* 3) Your centered “through-the-middle” custom line */
.miniCalendarWrapper .fc .fc-timegrid-slot:not(.fc-timegrid-slot-label)::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #c9d9e8;
  transform: translateY(-50%);
  z-index: 2;
}

/* 4) Native dashed divider ONLY on the event-slot cells */
.miniCalendarWrapper .fc .fc-timegrid-slot:not(.fc-timegrid-slot-label) {
  border-bottom: 1px dashed #C9D9E8 !important;
}

/* 5) Time-label cell: mask lines & push text down */
.miniCalendarWrapper .fc .fc-timegrid-slot-label {
  background-color: white;
  position: relative;
  z-index: 2;
  padding-left: 4px;
  padding-top: 15px;
  border: none;
}
/*    nudge the inner <div> down a bit more */
.miniCalendarWrapper .fc .fc-timegrid-slot-label > div {
  margin-top: 8px;
}

.miniCalendarWrapper .fc .fc-timegrid-slot .fc-timegrid-slot-lane {
  border-bottom: 1px dashed #C9D9E8 !important;
}

/* 6) Remove all outer/fullcalendar container borders */
.miniCalendarWrapper .fc {
  border: none !important;
  box-shadow: none !important;
}
/* inner scrollgrid wrapper */
.miniCalendarWrapper .fc-theme-standard .fc-scrollgrid {
  border: none !important;
}
/* view harness around the grid */
.miniCalendarWrapper .fc .fc-view-harness {
  border: none !important;
}
/* any leftover header/body cell borders */
.miniCalendarWrapper .fc .fc-col-header-cell,
.miniCalendarWrapper .fc .fc-timegrid-slots,
.miniCalendarWrapper .fc .fc-daygrid-body {
  border: none !important;
}
/* strip vertical lines on far left/right */
.miniCalendarWrapper .fc .fc-scrollgrid-section td {
  border-left: none !important;
  border-right: none !important;
}


/* 1) Remove any bottom border on the scrollgrid container */
.miniCalendarWrapper .fc-theme-standard .fc-scrollgrid {
  border-bottom: none !important;
}

/* 2) Target the last slot‐row in the table body */
.miniCalendarWrapper .fc .fc-timegrid-slots tbody tr:last-child td {
  border-bottom: none !important;
}

.miniCalendarWrapper .fc {
  border-bottom: none !important;
}