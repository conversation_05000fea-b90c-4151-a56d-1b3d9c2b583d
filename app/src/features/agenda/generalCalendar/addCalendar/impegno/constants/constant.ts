export const DEADLINE_UNIQUE_RANDOM = (
    Date.now() + new Date().getMilliseconds()
)
    .toString()
    .slice(-6);

export const HOURS = Array.from({ length: 24 }, (_, i) =>
    String(i).padStart(2, "0")
);
export const MINUTES = Array.from({ length: 60 }, (_, i) =>
    String(i).padStart(2, "0")
);

export const RIRTEMPLATE_DATA = [
    { name: "Giornalier<PERSON>", value: "daily" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", value: "weekly" },
    { name: "<PERSON><PERSON><PERSON>", value: "monthly" },
    { name: "Annuale", value: "yearly" },
];

export const CURRENT_DATE_FORMATTED = () => {
    const today = new Date();
    const day = String(today.getDate()).padStart(2, "0");
    const month = String(today.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
    const year = today.getFullYear();
    return `${day}/${month}/${year}`;
};

export const LAST_YEAR_START_DATE_FORMATTED = () => {
    const today = new Date();
    const lastYear = today.getFullYear() - 1;
    return `01/01/${lastYear}`;
};

export const CURRENT_DATE_END_FORMATTED = () => {
    return CURRENT_DATE_FORMATTED();
};

export const formatTimeHH = (date: Date): string =>
    String(date.getHours()).padStart(2, "0");

export const formatTimeMM = (date: Date): string =>
    String(date.getMinutes()).padStart(2, "0");

export const formatTimeHHMM = (
    date: Date
): { hours: string; minutes: string } => ({
    hours: formatTimeHH(date),
    minutes: formatTimeMM(date),
});

export const VERSIONS = [
    { value: "2022", label: "2022" },
    { value: "2018", label: "2018" },
    { value: "2014", label: "2014" },
    { value: "0", label: "Precedente" },
    { value: "-1", label: "Voce libera" },
];

export const WEEK_OPTIONS = [
    { label: "Dom", name: "riweeklyweekdaysSU", value: "SU" },
    { label: "Lun", name: "riweeklyweekdaysMO", value: "MO" },
    { label: "Mar", name: "riweeklyweekdaysTU", value: "TU" },
    { label: "Mer", name: "riweeklyweekdaysWE", value: "WE" },
    { label: "Gio", name: "riweeklyweekdaysTH", value: "TH" },
    { label: "Ven", name: "riweeklyweekdaysFR", value: "FR" },
    { label: "Sab", name: "riweeklyweekdaysSA", value: "SA" },
];

export const MONTH_OPTIONS = [
    { value: "1", label: "Gennaio" },
    { value: "2", label: "Febbraio" },
    { value: "3", label: "Marzo" },
    { value: "4", label: "Aprile" },
    { value: "5", label: "Maggio" },
    { value: "6", label: "Giugno" },
    { value: "7", label: "Luglio" },
    { value: "8", label: "Agosto" },
    { value: "9", label: "Settembre" },
    { value: "10", label: "Ottobre" },
    { value: "11", label: "Novembre" },
    { value: "12", label: "Dicembre" },
];

export const EXPENSE_TYPE = [
    { value: "0", label: "Non specificata" },
    { value: "1", label: "Spesa anticipata" },
    { value: "2", label: "Spesa imponibile" },
    { value: "5", label: "Spesa non imponibile" },
    { value: "3", label: "Spesa esente art" },
    { value: "4", label: "Spesa esclusa art" },
    { value: "10", label: "Giroconto" },
];

export const getCurrentTimeHHMM = () => formatTimeHHMM(new Date());
