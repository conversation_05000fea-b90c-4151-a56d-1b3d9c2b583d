import { IDeadlineParams } from "../interfaces/impegno.interface";
import {
    CURRENT_DATE_FORMATTED,
    DEADLINE_UNIQUE_RANDOM,
    getCurrentTimeHHMM,
} from "./constant";

export const DEFAULT_DEADLINE_STATUS = "0";
export const DEFAULT_DEADLINE_CATEGORY = "0";

const { hours: DEFAULT_DEADLINE_HOURS, minutes: DEFAULT_DEADLINE_MINUTES } = getCurrentTimeHHMM();

export const DEFAULT_DEADLINE_PARAMS: IDeadlineParams = {
    deadlineUniqueid: "",
    deadlineLinkuid: "",
    deadlineFileUniqueid: "",
    templates: "",
    dynamic: "0",
    macroInstanceUid: "",
    recurrenceId: "",
    connectedElements: "",
    idPratica: "",
    deadlineText: "", //required
    deadlineDate: CURRENT_DATE_FORMATTED(),
    deadlineHours: DEFAULT_DEADLINE_HOURS,
    deadlineMinutes: DEFAULT_DEADLINE_MINUTES,
    deadlineDaysBefore: 0,
    deadlinePeriod: "30",
    deadline<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: "0",
    deadlineSpeseesenti: "0",
    deadlineSpeseimponibili: "0",
    deadlineSpeseescluse: "0",
    deadlineType: "",
    deadlineCategory: DEFAULT_DEADLINE_CATEGORY,
    deadlineStatus: DEFAULT_DEADLINE_STATUS,
    deadLinesGroups: "-1",
    deadlineUser: [],
    deadlineAnnotation: "",
    deadlineAddebitabile: false,
    parentItemId: "",
    nuovoImpegno: "0",
    deadlineUniqueidToDeclare: DEADLINE_UNIQUE_RANDOM,
    deadlineVisible: false,
    endType: "BYOCCURRENCES",
    deadlineEvasa: false,
    deadlineNonevadere: false,
    deadlineImportant: false,
    deadlineBillable: false,
    deadlineFile: "",
    rirtemplate: "daily",
    ridailyinterval: "1",
    occurrencesN: "1",
    riweeklyinterval: "1",
    rimonthlyinterval: "1",
    rimonthlydayofmonthday: "1",
    riyearlyinterval: "1",
    riyearlydayofmonthmonth: "1",
    rirangebyenddatecalendar: CURRENT_DATE_FORMATTED(),
    privato: false,
    location: "",
};
