import React, {
    createContext,
    useContext,
    useState,
    useEffect,
    ReactNode,
    useRef,
} from "react";
import { useParams, useLocation } from "react-router-dom";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { IDeadlineParams } from "../interfaces/impegno.interface";
import { DEFAULT_DEADLINE_PARAMS } from "../constants/saveParams";
import { useAgendaParams } from "../../../../hooks/useAgendaParams.hook";
import { formatTimeHHMM } from "../constants/constant";

interface CalendarDataContextProps {
    data: any;
    usersGroupData: any;
    fetchUserGroupById: (id: string) => Promise<any>;
    loggedUserName: string;
    fetchCalendarData: () => Promise<void>;
    deadlineSaveParams: IDeadlineParams;
    setDeadlineSaveParams: React.Dispatch<
        React.SetStateAction<IDeadlineParams>
    >;
    selectedPraticaName: any | null;
    setSelectedPraticaName: React.Dispatch<React.SetStateAction<any | null>>;
    isUserEditing: boolean;
    recurrenceId: string | null;
    fetchAgain: (uniqueId?: string) => Promise<void>;
    loading: boolean;
    isExternal: boolean;
}

const CalendarDataContext = createContext<CalendarDataContextProps | undefined>(
    undefined
);

interface ProviderProps {
    children: ReactNode;
    type?: string;
}

export const CalendarDataProvider = ({ children, type }: ProviderProps) => {
    const { id }: any = useParams();
    const location = useLocation();
    const state = location.state as any;
    const fileUniqueid = state?.fileUniqueid;
    const hasFetchedRef = useRef(false);
    const { rowDataUrl } = useAgendaParams();
    const { hours, minutes } = formatTimeHHMM(new Date());

    // Initialize your custom requests
    const getCalendarDataRequest = useGetCustom("calendar/calendar");
    const getRowDataRequest =
        type === "adendaDeadline"
            ? usePostCustom(`${rowDataUrl}/getrowdata?noTemplateVars=true`)
            : usePostCustom("deadlines/getrowdata?noTemplateVars=true");

    const getUsersGroupRequest = useGetCustom(
        "deadlines/getusersgroup?noTemplateVars=true"
    );
    const getFileDataRequest = usePostCustom(
        "deadlines/getfiledata?noTemplateVars=true"
    );

    const [deadlineSaveParams, setDeadlineSaveParams] =
        useState<IDeadlineParams>(DEFAULT_DEADLINE_PARAMS);
    const [selectedPraticaName, setSelectedPraticaName] = useState<any | null>(
        null
    );
    const [recurrenceId, setRecurrenceId] = useState<string | null>(null);
    const [data, setData] = useState<any>({
        usersData: [],
        groupsData: [],
        impegnoData: [],
        tipologiaData: [],
        categoriaData: [],
        statoData: [],
        additionalItems: [],
        expense: [],
    });
    const [usersGroupData, setUsersGroupData] = useState<any>([]);
    const [loggedUserName, setLoggedUserDataName] = useState<string>("");
    const [isExternal, setIsExternal] = useState<boolean>(false);

    // Helper functions
    const getPraticaData = async (idPratica: string) => {
        const response: any = await getFileDataRequest.doFetch(true, {
            idPratica,
        });
        return response.data;
    };

    const replaceNullWithEmptyString = (obj: any) => {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [
                key,
                value === null ? "" : value,
            ])
        );
    };

    const updateDefaultDeadlineUser = (data: any, loggedUserName: string) => {
        const { hours, minutes } = formatTimeHHMM(new Date());
        // If editing an existing impegno, use the saved user
        if (data?.impegnoData?.deadlineUser?.[0]?.id) {
            const checkedUser = data.usersData.find(
                (user: any) => user.id === data.impegnoData.deadlineUser[0].id
            );
            if (checkedUser) {
                setDeadlineSaveParams((prevParams) => ({
                    ...prevParams,
                    deadlineUser:
                        prevParams.deadlineUser &&
                        prevParams.deadlineUser.length > 0
                            ? prevParams.deadlineUser
                            : [
                                  {
                                      id: checkedUser.id,
                                      nomeutente: checkedUser.nomeutente,
                                  },
                              ],
                    deadlineHours:
                        prevParams.deadlineHours &&
                        prevParams.deadlineHours !== "" &&
                        prevParams.deadlineHours !== "0" &&
                        prevParams.deadlineHours !== "00"
                            ? prevParams.deadlineHours
                            : hours,
                    deadlineMinutes:
                        prevParams.deadlineMinutes &&
                        prevParams.deadlineMinutes !== "" &&
                        prevParams.deadlineMinutes !== "0" &&
                        prevParams.deadlineMinutes !== "00"
                            ? prevParams.deadlineMinutes
                            : minutes,
                }));
                return;
            }
        }

        // For new impegno, use the current logged-in user
        const loggedUser = data.usersData.find(
            (user: any) => user.nomeutente === loggedUserName
        );

        if (loggedUser) {
            setDeadlineSaveParams((prevParams) => ({
                ...prevParams,
                deadlineUser:
                    prevParams.deadlineUser &&
                    prevParams.deadlineUser.length > 0
                        ? prevParams.deadlineUser
                        : [
                              {
                                  id: loggedUser.id,
                                  nomeutente: loggedUser.nomeutente,
                              },
                          ],
                deadlineHours:
                    prevParams.deadlineHours &&
                    prevParams.deadlineHours !== "" &&
                    prevParams.deadlineHours !== "0" &&
                    prevParams.deadlineHours !== "00"
                        ? prevParams.deadlineHours
                        : hours,
                deadlineMinutes:
                    prevParams.deadlineMinutes &&
                    prevParams.deadlineMinutes !== "" &&
                    prevParams.deadlineMinutes !== "0" &&
                    prevParams.deadlineMinutes !== "00"
                        ? prevParams.deadlineMinutes
                        : minutes,
            }));
        }
    };

    const updateDefaultDeadlineType = (data: any) => {
        const firstValue = data.tipologiaData[0];
        setDeadlineSaveParams((prevParams) => ({
            ...prevParams,
            deadlineType:
                prevParams.deadlineType && prevParams.deadlineType !== ""
                    ? prevParams.deadlineType
                    : firstValue?.id,
        }));
    };

    // Initial data fetch effect
    useEffect(() => {
        if (!hasFetchedRef.current) {
            hasFetchedRef.current = true;
            fetchData();
        }
    }, []);

    // Update default deadline values when there's no id from params
    useEffect(() => {
        if (id === undefined) {
            updateDefaultDeadlineUser(data, loggedUserName);
            updateDefaultDeadlineType(data);
            // Removed redundant setDeadlineSaveParams for deadlineHours and deadlineMinutes
        }
    }, [id, data, loggedUserName]);

    const fetchData = async (uniqueId?: string) => {
        const [rowDataResponse, calendarResponse]: any = await Promise.all([
            getRowDataRequest.doFetch(true, {
                uniqueId: uniqueId || id,
                fileUniqueid: fileUniqueid,
            }),
            getCalendarDataRequest.doFetch(true),
        ]);
        const { form, groups, users } = rowDataResponse.data;
        const {
            deadlineTypes,
            deadlineCategories,
            deadlineStatus,
            items,
            loggedUser,
            listini,
            methods,
            spese_fisse,
        } = calendarResponse.data;

        if (typeof id === "string" || uniqueId) {
            const transformedForm = {
                ...replaceNullWithEmptyString(form),
                deadlineEvasa: form.deadlineEvasa === "1",
                deadlineNonevadere: form.deadlineNonevadere === "1",
                deadlineImportant: form.deadlineImportant === "1",
                deadlineBillable: form.deadlineBillable === "1",
                deadlineVisible: form.deadlineVisible === "1",
                deadlineAddebitabile: form.deadlineAddebitabile === "1",
            };

            if (form.idPratica !== "") {
                const praticaData: any = await getPraticaData(form.idPratica);
                setSelectedPraticaName(praticaData);
            }
            setRecurrenceId(form.recurrenceId);
            setDeadlineSaveParams((prev) => ({
                ...DEFAULT_DEADLINE_PARAMS,
                ...form,
                ...transformedForm,
                deadlineType:
                    prev.deadlineType && prev.deadlineType !== ""
                        ? prev.deadlineType
                        : form.deadlineType,
                deadlineHours:
                    prev.deadlineHours &&
                    prev.deadlineHours !== "" &&
                    prev.deadlineHours !== "0" &&
                    prev.deadlineHours !== "00"
                        ? prev.deadlineHours
                        : hours,
                deadlineMinutes:
                    prev.deadlineMinutes &&
                    prev.deadlineMinutes !== "" &&
                    prev.deadlineMinutes !== "0" &&
                    prev.deadlineMinutes !== "00"
                        ? prev.deadlineMinutes
                        : minutes,
            }));
        }

        const newData = {
            usersData: users,
            groupsData: groups,
            impegnoData: form,
            tipologiaData: deadlineTypes,
            categoriaData: deadlineCategories,
            statoData: deadlineStatus,
            additionalItems: items,
            listData: listini,
            expenseMethods: methods,
            expenseSpeseFisse: spese_fisse,
        };

        setData(newData);
        setLoggedUserDataName(loggedUser?.nomeutente);
        setIsExternal(loggedUser?.isExternal);

        // If creating a new impegno (no id), set the current user as the default intestatario
        if (id === undefined && !uniqueId) {
            // Find the current user in the users list
            const currentUser = users.find(
                (user: any) => user.nomeutente === loggedUser?.nomeutente
            );

            if (currentUser) {
                setDeadlineSaveParams((prevParams) => ({
                    ...prevParams,
                    deadlineUser:
                        prevParams.deadlineUser &&
                        prevParams.deadlineUser.length > 0
                            ? prevParams.deadlineUser
                            : [
                                  {
                                      id: currentUser.id,
                                      nomeutente: currentUser.nomeutente,
                                  },
                              ],
                    deadlineHours:
                        prevParams.deadlineHours &&
                        prevParams.deadlineHours !== "" &&
                        prevParams.deadlineHours !== "0" &&
                        prevParams.deadlineHours !== "00"
                            ? prevParams.deadlineHours
                            : hours,
                    deadlineMinutes:
                        prevParams.deadlineMinutes &&
                        prevParams.deadlineMinutes !== "" &&
                        prevParams.deadlineMinutes !== "0" &&
                        prevParams.deadlineMinutes !== "00"
                            ? prevParams.deadlineMinutes
                            : minutes,
                }));
            }
        }
    };

    const fetchUserGroupById = async (groupId: string) => {
        const response: any = await getUsersGroupRequest.doFetch(true, {
            idGruppo: groupId,
        });
        setUsersGroupData(response.data);
        return response.data;
    };

    // Function to fetch only calendar general data
    const fetchCalendarData = async () => {
        const response: any = await getCalendarDataRequest.doFetch(true);
        const { deadlineTypes, deadlineCategories, deadlineStatus, items } =
            response.data;
        setData((prevData: any) => ({
            ...prevData,
            tipologiaData: deadlineTypes,
            categoriaData: deadlineCategories,
            statoData: deadlineStatus,
            additionalItems: items,
        }));
    };

    return (
        <CalendarDataContext.Provider
            value={{
                data,
                usersGroupData,
                fetchUserGroupById,
                loggedUserName,
                fetchCalendarData,
                deadlineSaveParams,
                setDeadlineSaveParams,
                selectedPraticaName,
                setSelectedPraticaName,
                isUserEditing: typeof id === "string",
                recurrenceId,
                fetchAgain: fetchData,
                loading: getRowDataRequest.loading,
                isExternal,
            }}
        >
            {children}
        </CalendarDataContext.Provider>
    );
};

export const useCalendarData = () => {
    const context = useContext(CalendarDataContext);
    if (context === undefined) {
        throw new Error(
            "useCalendarData must be used within a CalendarDataProvider"
        );
    }
    return context;
};
