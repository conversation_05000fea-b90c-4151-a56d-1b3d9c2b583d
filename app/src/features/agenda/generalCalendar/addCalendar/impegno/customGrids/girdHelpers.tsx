import { Box, Button } from "@vapor/react-material";
import { TYPES } from "../hooks/useSavePerformance";
import DownloadIcon from "@mui/icons-material/Download";
import LinkOffIcon from "@mui/icons-material/LinkOff";
import bulletGreen from "../../../../../../assets/images/bulletGreen.png";
import bulletRed from "../../../../../../assets/images/bulletRed.png";

export const deleteAnagraficheButton = (
    row: any,
    handleRemoveAnagrafiche: any,
    t?: any
) => {
    return (
        <Button
            variant="outlined"
            onClick={(event: any) => {
                event.stopPropagation(); // Prevent grid-level click interference
                handleRemoveAnagrafiche(row); // Pass the correct row.id
            }}
            color="error"
            size="small"
        >
            {t("Elimina")}
        </Button>
    );
};

export const deletePerformanceButton = (
    child_id: string,
    type: string,
    handleRemovePerformance: any,
    t?: any
) => {
    return (
        <Button
            variant="outlined"
            onClick={() => {
                handleRemovePerformance(child_id, type); // Pass the correct row.id
            }}
            color="error"
            size="small"
        >
            {t("Elimina")}
        </Button>
    );
};

export const showBillableStatus = (status: any) => {
    return (
        <Box display="flex" alignItems="center">
            <img
                alt="not found"
                src={status === "1" ? bulletGreen : bulletRed}
                style={{ marginRight: "8px" }}
            />
            {status === "1" ? "Yes" : "No"}
        </Box>
    );
};

export const showEscapedStatus = (status: any) => {
    const isGreen = status === "1";
    const isRed = status === "0";

    return isGreen || isRed ? (
        <Box display="flex" alignItems="center">
            <img
                alt="not found"
                src={isGreen ? bulletGreen : bulletRed}
                style={{ marginRight: "8px" }}
            />
            {isGreen ? "Si" : "No"}
        </Box>
    ) : (
        ""
    );
};

export function convertMinutesToHHMM(minutes: number) {
    const hours = Math.floor(minutes / 60); // Calculate hours
    const mins = minutes % 60; // Calculate remaining minutes
    return `${hours}:${mins.toString().padStart(2, "0")} h`; // Format as hh:mm
}

export const formatAmount = (rowData: any) => {
    const { row } = rowData;
    if (parseInt(row.tipo) === TYPES.timesheet) {
        return convertMinutesToHHMM(parseInt(row.quantita));

    } else {
        return row.quantita;
    }
};


export const formatNumber = (number: any) => {
    return (
        number?.toLocaleString("it-IT", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }) + " €"
    );
};

export const calculateTotal = (rowData: any) => {
    const { row } = rowData;
    var totale = 0;
    if (row.tipo != 1) {
        // NON timesheet
        totale = parseFloat(row.valore) * parseInt(row.quantita);
    } else {
        let hours = Math.floor(row.quantita / 60);
        let minutes = row.quantita % 60;
        totale = parseFloat(row.valore) * hours;
        let ratio = minutes / 60;
        totale = totale + parseFloat(row.valore) * parseFloat(ratio.toString());
    }
    return formatNumber(totale);
};

export const renderDownloadButton = (
    id: any,
    getFile: (uniqueid: string) => void,
    t: any
) => {
    return (
        <Button startIcon={<DownloadIcon />} onClick={() => getFile(id)}>
            {t("Download")}
        </Button>
    );
};

export const renderUnlinkButton = (
    id: any,
    unlinkDocument: (id: string) => void,
    t: any
) => {
    return (
        <Button
            color="error"
            startIcon={<LinkOffIcon />}
            onClick={() => unlinkDocument(id)}
        >
            {t("Scollega")}
        </Button>
    );
};

export const formatDate = (date: string) => {
    return date.split(" ")[0].replace(/-/g, "/");
};
