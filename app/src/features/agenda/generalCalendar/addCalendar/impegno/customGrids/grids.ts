import BaseGridList from "../../../../../../models/BaseGridList";
import { IGridColumn } from "../../../../../../interfaces/general.interfaces";
import {
    mapAnagraficheColumnNamesGRID,
    mapDocumentsColumnNameGRID,
    mapImpegniCollegatiColumnNameGRID,
    mapPerformanceColumnNameGRID,
} from "./mapColumns";
import { mapOtherColumnNames } from "../../../../../../utilities/common";

export const getDocumentsGrid = async (
    t: any,
    getFile: (uniqueid: string) => void,
    unlinkDocument: (id: string) => void
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome file"), t("Descrizione"), t("Data"), "", ""],
            column_keys: [
                "nomefile",
                "titolodocumento",
                "data",
                "download",
                "action",
            ],
            column_widths: ["100px", "100px", "100px", "50px", "50px"],
            sortable: [false, false, false, false, false],
            cell_templates: [null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapDocumentsColumnNameGRID(response, getFile, unlinkDocument, t);
};

export const getAnagraficheGrid = async (
    t: any,
    handleRemoveAnagrafiche: (id: string) => void,
    isUserEditing: boolean
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Denominazione"), t("C.F."), t("Partita iva"), ""],
            column_keys: [
                isUserEditing ? "nome" : "denominazione",
                "codicefiscale",
                "partitaiva",
                "action",
            ],
            column_widths: ["40%", "30%", "20%", "10%"],
            sortable: [false, false, false, false],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapAnagraficheColumnNamesGRID(
        response,
        handleRemoveAnagrafiche,
        isUserEditing,
        t
    );
};

export const getModelsGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: ["", t("Nome modello")],
            column_keys: ["uniqueid", "title"],
            column_widths: ["0%", "100%"],
            sortable: [false, false],
            cell_templates: [null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNames(response);
};

export const getImpegniCollegatiGrid = async (
    t: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Descrizione"),
                t("Data attuale"),
                t("Data modifica"),
            ],
            column_keys: ["description", "olddate", "date"],
            column_widths: ["50%", "25%", "25%"],
            sortable: [false, false, false],
            cell_templates: [null, null, false],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapImpegniCollegatiColumnNameGRID(response);
};

export const getPrestazioniGrid = async (
    handleDeletePerformance: (id: string, type: string) => void,
    t: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Data"),
                t("Utente"),
                t("Descrizione"),
                t("Qta"),
                t("Importo"),
                t("Totale"),
                t("Tipo"),
                t("Evaso"),
                t("Fatturabile"),
                "",
            ],
            column_keys: [
                "formattedDate",
                "nomeutente",
                "nome",
                "quantita",
                "valore",
                "total",
                "tipologia",
                "evasa",
                "fatturabile",
                "action",
            ],
            column_widths: [
                "50%",
                "50%",
                "50%",
                "50%",
                "50%",
                "50%",
                "50%",
                "50%",
                "50%",
                "60px",
            ],
            sortable: [
                false,
                false,
                false,
                false,
                false,
                false,
                false,
                false,
                false,
                false,
            ],
            cell_templates: [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
            ],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapPerformanceColumnNameGRID(response, handleDeletePerformance, t);
};

export const getListGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: ["", t("Descrizione"), t("Prezzo")],
            column_keys: ["", "descrizione", "prezzo"],
            column_widths: ["20", "40%", "40%"],
            sortable: [false, false, false],
            cell_templates: [null, null, false],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapOtherColumnNames(response);
};
