import {
    IGridColumn,
    IGridSettings,
} from "../../../../../../interfaces/general.interfaces";
import { mapOtherList } from "../../../../../../utilities/common";
import {
    formatDate,
    calculateTotal,
    deleteAnagraficheButton,
    deletePerformanceButton,
    formatAmount,
    formatNumber,
    renderDownloadButton,
    showBillableStatus,
    showEscapedStatus,
    renderUnlinkButton,
} from "./girdHelpers";

export const mapAnagraficheColumnNamesGRID = (
    response: any,
    handleRemoveAnagrafiche: (id: string) => void,
    isUserEditing: boolean,
    t?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "action") {
                returnColumn.renderCell = (row: any) =>
                    deleteAnagraficheButton(
                        !isUserEditing ? row.row.id : row.row.uniqueid,
                        handleRemoveAnagrafiche,
                        t
                    );
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapDocumentsColumnNameGRID = (
    response: any,
    getFile: (uniqueid: string) => void,
    unlinkDocument: (id: string) => void,
    t?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "download") {
                returnColumn.renderCell = (row: any) =>
                    renderDownloadButton(row.row.uniqueid, getFile, t);
            } else if (column_keys[index] === "action") {
                returnColumn.renderCell = (row: any) =>
                    renderUnlinkButton(row.row.id, unlinkDocument, t);
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapImpegniCollegatiColumnNameGRID = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "olddate") {
                returnColumn.renderCell = (row: any) =>
                    formatDate(row?.row.olddate);
            } else if (column_keys[index] === "date") {
                returnColumn.renderCell = (row: any) =>
                    formatDate(row?.row.date);
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapPerformanceColumnNameGRID = (
    response: any,
    handleDeletePerformance: (id: string, type: string) => void,
    t?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "fatturabile") {
                returnColumn.renderCell = (row: any) =>
                    showBillableStatus(row.fatturabile);
            } else if (column_keys[index] === "evasa") {
                returnColumn.renderCell = (row: any) =>
                    showEscapedStatus(row.evasa);
            } else if (column_keys[index] === "quantita") {
                returnColumn.renderCell = (row: any) => formatAmount(row);
            } else if (column_keys[index] === "valore") {
                returnColumn.renderCell = (row: any) =>
                    formatNumber(row?.row?.valore);
            } else if (column_keys[index] === "action") {
                returnColumn.renderCell = (row: any) =>
                    deletePerformanceButton(
                        row?.row?.child_id,
                        row?.row?.tipo,
                        handleDeletePerformance,
                        t
                    );
            } else if (column_keys[index] === "total") {
                returnColumn.renderCell = (row: any) => calculateTotal(row);
            }
            return returnColumn;
        }
    });
    columns = columns
        .filter(function (element: any) {
            return element !== undefined;
        })
        .filter(Boolean) as IGridColumn[];
    return columns;
};

export const mapDocumentsColumns = (
    gridSettings: IGridSettings,
    t: any,
    getFile: (uniqueid: string) => void,
    unlinkDocument: (id: string) => void
): IGridColumn[] => {
    const { column_names, column_keys, sortable, column_widths } = gridSettings;

    return column_names
        .map((name: string, index: number) => {
            if (column_widths && column_widths[index] !== "0%") {
                return {
                    name,
                    selector: (row: any) => {
                        switch (column_keys[index]) {
                            case "download":
                                return renderDownloadButton(
                                    row.uniqueid,
                                    getFile,
                                    t
                                );
                            case "action":
                                return renderUnlinkButton(
                                    row.id,
                                    unlinkDocument,
                                    t
                                );
                            default:
                                return row[column_keys[index]];
                        }
                    },
                    sortField: column_keys[index],
                    sortable: sortable[index],
                    width: column_widths[index],
                };
            }
            return null;
        })
        .filter(Boolean) as IGridColumn[];
};

export const mapImpegniCollegatiColumns = (
    gridSettings: IGridSettings
): IGridColumn[] => {
    const { column_names, column_keys, sortable, column_widths } = gridSettings;

    return column_names
        .map((name: string, index: number) => {
            if (column_widths && column_widths[index] !== "0%") {
                return {
                    name,
                    selector: (row: any) => {
                        switch (column_keys[index]) {
                            case "olddate":
                                return formatDate(row.olddate);
                            case "date":
                                return formatDate(row.date);
                            default:
                                return row[column_keys[index]];
                        }
                    },
                    sortField: column_keys[index],
                    sortable: sortable[index],
                    width: column_widths[index],
                };
            }
            return null;
        })
        .filter(Boolean) as IGridColumn[];
};
