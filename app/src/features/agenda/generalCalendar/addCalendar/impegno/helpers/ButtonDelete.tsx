import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>u, MenuItem } from "@vapor/v3-components";
import { useTranslation } from "@1f/react-sdk";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import useDeleteDeadline from "../hooks/useDeleteDeadline";
import { useNavigate } from "react-router-dom";

interface IProps {
    showDropdown: boolean;
    data: any;
    showModal: any;
    setShowModal: React.Dispatch<React.SetStateAction<any>>;
    navigateBackUrl?: string;
}

export default function ButtonDelete({
    showDropdown,
    data,
    showModal,
    setShowModal,
    navigateBackUrl
}: IProps) {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const { handleDeleteDeadline, handleDeleteFullReferenceRequest } =
        useDeleteDeadline();

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleDelete = async () => {
        const params = {
            uniqueid: data.deadlineUniqueid,
            updateActivePage: false,
            macroinstanceUniqueId: data.macroInstanceUid,
            dynamic: data.dynamic === null ? "" : data.dynamic,
        };

        await handleDeleteDeadline(params);
        setShowModal({ ...showModal, open: false });
        navigateBackUrl && navigate(navigateBackUrl);

    };

    const handleDeleteFullReference = async () => {
        const params = {
            recurrenceId: data.deadlineUniqueid,
            updateActivePage: false,
            macroinstanceUniqueId: data.macroInstanceUid,
            dynamic: data.dynamic === null ? "" : data.dynamic,
        };

        await handleDeleteFullReferenceRequest(params);
        setShowModal({ ...showModal, open: false });
        navigateBackUrl && navigate(navigateBackUrl);
    };

    const openModal = (deleteAll: boolean = false) => {
        setShowModal({
            open: true,
            title: t("Elimina"),
            confirmText: deleteAll
                ? t("Eliminare definitivamente la serie ?")
                : t("Eliminare definitivamente l'impegno ?"),
            deleteFunc: deleteAll
                ? () => handleDeleteFullReference()
                : () => handleDelete(),
        });
    };

    return (
        <>
            {showDropdown ? (
                <div>
                    <Button
                        aria-haspopup="true"
                        variant="outlined"
                        color="error"
                        endIcon={<ArrowDropUpIcon />}
                        onClick={handleClick}
                        aria-controls={open ? "basic-menu" : undefined}
                        aria-expanded={open ? "true" : undefined}
                    >
                        {t("Elimina")}
                    </Button>
                    <Menu
                        MenuListProps={{
                            "aria-labelledby": "basic-button",
                        }}
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                        transformOrigin={{
                            vertical: "bottom",
                            horizontal: "center",
                        }}
                        anchorOrigin={{
                            vertical: "top",
                            horizontal: "center",
                        }}
                    >
                        <MenuItem onClick={() => openModal()}>
                            {t("Elimina singolo impegno")}
                        </MenuItem>
                        <MenuItem onClick={() => openModal(true)}>
                            {t("Elimina tutta la serie")}
                        </MenuItem>
                    </Menu>
                </div>
            ) : (
                <Button
                    color="error"
                    onClick={() => openModal()}
                    variant="outlined"
                >
                    {t("Elimina")}
                </Button>
            )}
        </>
    );
}
