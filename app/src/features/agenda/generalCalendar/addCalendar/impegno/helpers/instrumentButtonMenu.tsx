import { Button, Menu, MenuItem } from "@vapor/v3-components";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { useTranslation } from "@1f/react-sdk";

interface InstrumentButtonMenuProps {
    onClickFunc: () => void;
    isUserEditing: boolean;
    recurrenceId: string;
    deadlineFileUniqueid: string;
    handlePrintRequest: (uniqueid: string) => void;
    deadlineUniqueid: string;
}

export default function InstrumentButtonMenu({
    onClickFunc,
    isUserEditing,
    recurrenceId,
    deadlineFileUniqueid,
    handlePrintRequest,
    deadlineUniqueid,
}: InstrumentButtonMenuProps) {
    const {t} = useTranslation();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };
    const navigate = useNavigate();

    return (
        <div>
            <Button
                aria-haspopup="true"
                variant="outlined"
                endIcon={<ArrowDropUpIcon />}
                onClick={handleClick}
                aria-controls={open ? "basic-menu" : undefined}
                aria-expanded={open ? "true" : undefined}
            >
                {t("Strumenti")}
            </Button>
            <Menu
                MenuListProps={{
                    "aria-labelledby": "basic-button",
                }}
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                transformOrigin={{
                    vertical: "bottom",
                    horizontal: "center",
                }}
                anchorOrigin={{
                    vertical: "top",
                    horizontal: "center",
                }}
            >
                {isUserEditing && deadlineFileUniqueid && (
                    <>
                        <MenuItem
                            onClick={() =>
                                navigate(
                                    `/legacy/archive/summary?uid=${deadlineFileUniqueid}`
                                )
                            }
                        >
                            {t("Vai alla pratica")}
                        </MenuItem>
                        <MenuItem
                            onClick={() => handlePrintRequest(deadlineUniqueid)}
                        >
                            {t("Stampa")}
                        </MenuItem>
                    </>
                )}
                {isUserEditing && deadlineUniqueid && (
                    <MenuItem
                        onClick={() => handlePrintRequest(deadlineUniqueid)}
                    >
                        {t("Stampa")}
                    </MenuItem>
                )}

                {/* Corrected recurrenceId condition */}
                {(!isUserEditing || recurrenceId === "") && (
                    <MenuItem onClick={onClickFunc}>{t("Ricorrenza")}</MenuItem>
                )}
            </Menu>
        </div>
    );
}
