import { useEffect } from "react";
import {
    DEFAULT_DEADLINE_STATUS,
    DEFAULT_DEADLINE_CATEGORY,
} from "../constants/saveParams";
import { ISelectedValue } from "../../../../../../custom-components/TextInputWithClearButton";

interface IProps {
    data: any;
    setSelectedTipologia: React.Dispatch<React.SetStateAction<ISelectedValue | null>>;
    setSelectedCategoria: React.Dispatch<React.SetStateAction<ISelectedValue | null>>;
    setSelectedStato: React.Dispatch<React.SetStateAction<ISelectedValue | null>>;
    selectedTipologia: ISelectedValue | null;
    selectedCategoria: ISelectedValue | null;
    selectedStato: ISelectedValue | null;
    clearSessionStorage: () => void;
    deadlineSaveParams: any;
    setDeadlineSaveParams: any;
}

const useDeadlineSelection = (props: IProps) => {
    const {
        deadlineSaveParams,
        data,
        setSelectedTipologia,
        setSelectedCategoria,
        setSelectedStato,
        selectedCategoria,
        selectedStato,
        clearSessionStorage,
        setDeadlineSaveParams,
    } = props;

    const getSelectedTipologiaById = (id: string) =>
        data?.tipologiaData.find((opt: any) => opt.id === id) || null;

    const getSelectedCategoriaById = (id: string) =>
        data?.categoriaData.find((opt: any) => opt.id === id) || null;

    const getSelectedStatoById = (id: string) =>
        data?.statoData.find((opt: any) => opt.id === id) || null;

    useEffect(() => {
        if (data?.tipologiaData) {
            if (deadlineSaveParams.deadlineType) {
                setSelectedTipologia(
                    getSelectedTipologiaById(deadlineSaveParams.deadlineType)
                );
            } else {
                setSelectedTipologia(null);
            }
        }
    }, [data?.tipologiaData, deadlineSaveParams.deadlineType]);

    useEffect(() => {
        if (deadlineSaveParams.deadlineCategory !== DEFAULT_DEADLINE_CATEGORY) {
            setSelectedCategoria(
                getSelectedCategoriaById(deadlineSaveParams.deadlineCategory)
            );
        }
    }, [deadlineSaveParams.deadlineCategory, data?.categoriaData]);

    useEffect(() => {
        if (deadlineSaveParams.deadlineStatus !== DEFAULT_DEADLINE_STATUS) {
            setSelectedStato(
                getSelectedStatoById(deadlineSaveParams.deadlineStatus)
            );
        }
    }, [deadlineSaveParams.deadlineStatus, data?.statoData]);

    useEffect(() => {
        if (selectedCategoria === null) {
            setDeadlineSaveParams({
                ...deadlineSaveParams,
                deadlineCategory: DEFAULT_DEADLINE_CATEGORY,
            });
            clearSessionStorage();
        }
    }, [selectedCategoria]);

    useEffect(() => {
        if (selectedStato === null) {
            clearSessionStorage();
            setDeadlineSaveParams({
                ...deadlineSaveParams,
                deadlineStatus: DEFAULT_DEADLINE_STATUS,
            });
        }
    }, [selectedStato]);
};

export default useDeadlineSelection;
