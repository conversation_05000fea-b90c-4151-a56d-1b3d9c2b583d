import usePostCustom from "../../../../../../hooks/usePostCustom";

export default function useDeleteDeadline() {
    const deleteRequest = usePostCustom(
        "deadlines/delete?noTemplateVars=true"
    );

    const deleteFullReferenceRequest = usePostCustom(
        "deadlines/delete-full-recurrence?noTemplateVars=true"
    );

  const handleDeleteDeadline = async (params: any) => {
    const response: any = await deleteRequest.doFetch(true, params);
    return response.data;
  };

  const handleDeleteFullReferenceRequest = async (params: any) => {
    const response: any = await deleteFullReferenceRequest.doFetch(
      true,
      params
    );
    return response.data;
  };

  return { handleDeleteDeadline, handleDeleteFullReferenceRequest };
}
