import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useState, useCallback, useEffect } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useCalendarData } from "../context/CalendarDataContext";

export default function useFetchDatiAggiuntivi() {
    const { data, deadlineSaveParams, setDeadlineSaveParams }: any =
        useCalendarData();
    const getParentDataRequest = useGetCustom(
        "item/get-parent-data?noTemplateVars=true"
    );
    const linkItemRequest = usePostCustom("item/link-item?noTemplateVars=true");
    const unlinkItemRequest = usePostCustom(
        "item/unlink-item?noTemplateVars=true"
    );

    const [loadingRequest, setLoadingRequest] = useState<boolean>(true);
    const [connect, setConnect] = useState<boolean>(false);
    const [selectedItem, setSelectedItem] = useState<any>(null);

    // Local state for parentItemId
    const [localParentItemId, setLocalParentItemId] = useState<string>("");

    useEffect(() => {
        if (
            deadlineSaveParams.parentItemId === "" &&
            data?.additionalItems?.[0]?.id
        ) {
            setLocalParentItemId(data.additionalItems[0].id);
            setConnect(false); // Ensure it's disconnected initially
        } else if (deadlineSaveParams.parentItemId !== "") {
            setLocalParentItemId(deadlineSaveParams.parentItemId);
            setConnect(true); // If the parentId is set, we assume it's connected
        } else {
            setConnect(false); // Default state for no connection
        }
    }, [data?.additionalItems]);

    // Effect to fetch data based on the parentItemId
    useEffect(() => {
        const fetch = async () => {
            const id = localParentItemId || data?.additionalItems?.[0]?.id;
            if (id) {
                const response = await fetchDatiAggiuntivi(id);
                setSelectedItem(response);
            }
        };
        fetch();
    }, [localParentItemId, data?.additionalItems]);

    const fetchDatiAggiuntivi = useCallback(async (id: string) => {
        setLoadingRequest(true);
        const response: any = await getParentDataRequest.doFetch(true, {
            itemId: id,
        });
        const { data } = response;
        setLoadingRequest(false);
        return data;
    }, []);

    const toggleItemConnection = async (parentId: string) => {
        const isConnect = connect; // Determine if the item is currently connected
        const request = isConnect ? unlinkItemRequest : linkItemRequest;

        const response = await request.doFetch(true, {
            parentId,
            childUid: deadlineSaveParams.deadlineUniqueid,
        });

        if (response) {
            if (isConnect) {
                setDeadlineSaveParams({
                    ...deadlineSaveParams,
                    parentItemId: "",
                });
            } else if (!isConnect) {
                setDeadlineSaveParams({
                    ...deadlineSaveParams,
                    parentItemId: localParentItemId,
                });
            }
            setConnect(!isConnect);
        }
    };

    return {
        fetchDatiAggiuntivi,
        loadingDatiAggiuntivi: loadingRequest,
        connect,
        setConnect,
        selectedItem,
        setSelectedItem,
        deadlineSaveParams,
        setDeadlineSaveParams,
        localParentItemId,
        setLocalParentItemId,
        toggleItemConnection,
    };
}
