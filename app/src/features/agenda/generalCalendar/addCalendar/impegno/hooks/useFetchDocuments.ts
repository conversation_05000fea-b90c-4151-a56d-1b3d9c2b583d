import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useCallback, useEffect, useState } from "react";
import { getDocumentsGrid } from "../customGrids/grids";
import { useTranslation } from "@1f/react-sdk";
import { IList } from "../../../../../../interfaces/general.interfaces";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useCalendarData } from "../context/CalendarDataContext";

const INITIAL_QUERY = {
    page: 0,
    pageSize: 9,
    sortColumn: "data",
    sortOrder: "desc",
    linkedDocsObjUid: "",
};

const LINK_DOCUMENT = {
    callback_function: "deadlineDetail",
    notinfile_add_url: "/documents/documents",
    obj_uid: "", //creation form
};

export default function useFetchDocuments() {
    const { t } = useTranslation();
    const { deadlineSaveParams, isUserEditing } = useCalendarData();
    const getDocumentsRequest = useGetCustom(
        "deadlines/documentslist?noTemplateVars=true"
    );
    const unlinkDocumentRequest = usePostCustom(
        "deadlines/unlink-doc?noTemplateVars=true"
    );

    const downloadDocumentsRequest = useGetCustom(
        "documents/getfile?noTemplateVars=true",
        {},
        null,
        true
    );
    const [listDocuments, setListDocuments] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });

    const [documentsQuery, setDocumentsQuery] = useState<any>(INITIAL_QUERY);

    useEffect(() => {
        if (!isUserEditing || !deadlineSaveParams.deadlineUniqueid) return;

        const updatedQuery = {
            ...documentsQuery,
            linkedDocsObjUid: deadlineSaveParams.deadlineUniqueid,
        };
        setDocumentsQuery(updatedQuery);
        // Update link document reference
        LINK_DOCUMENT.obj_uid = deadlineSaveParams.deadlineUniqueid;

        fetchDocumentsData(updatedQuery);
    }, [deadlineSaveParams.deadlineUniqueid, isUserEditing]);

    const getFile = async (uniqueid: string) => {
        try {
            const response: any = await downloadDocumentsRequest.doFetch(true, {
                uniqueid,
            });

            const contentType =
                response.headers["content-type"] || "application/octet-stream";
            const fileExtension = contentType.split("/")[1] || "bin";

            const contentDisposition = response.headers["content-disposition"];
            const filename = contentDisposition
                ? contentDisposition.split("filename=")[1]?.replace(/"/g, "") ||
                  `file.${fileExtension}`
                : `file_${uniqueid}.${fileExtension}`;

            const blob = new Blob([response.data], { type: contentType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", filename);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            return true;
        } catch (error) {
            console.error("Error downloading the file:", error);
            return false;
        }
    };

    const unlinkDocument = async (id: string) => {
        const response: any = await unlinkDocumentRequest.doFetch(true, {
            id,
            obj_uid: documentsQuery.linkedDocsObjUid,
        });
        if (response) {
            fetchDocumentsData();
        }
    };
    const fetchDocumentsData = useCallback(
        async (queryToUse = null) => {
            try {
                // Use the passed query parameter or fall back to the current state
                const queryParams = queryToUse || documentsQuery;

                const [columns, response]: any = await Promise.all([
                    getDocumentsGrid(t, getFile, unlinkDocument),
                    getDocumentsRequest.doFetch(true, queryParams),
                ]);

                const { currentPage, totalRows } = response.data;

                setListDocuments((prevList) => ({
                    ...prevList,
                    rows: currentPage,
                    columns,
                    totalRows: parseInt(totalRows),
                    page: queryParams?.page,
                    pageSize: queryParams?.pageSize,
                }));
            } catch (error) {
                console.error("Error in fetch documents", error);
            }
        },
        [t, getFile, unlinkDocument]
    );

    useEffect(() => {
        if (!isUserEditing || !documentsQuery.linkedDocsObjUid) return;
        // Only fetch if we have a valid linkedDocsObjUid and the user is editing
        fetchDocumentsData();
    }, [
        documentsQuery.page,
        documentsQuery.pageSize,
        documentsQuery.sortColumn,
        documentsQuery.sortOrder,
        isUserEditing,
    ]);

    return {
        INITIAL_QUERY,
        documentsQuery,
        setDocumentsQuery,
        listDocuments,
        setListDocuments,
        loadingDocuments: getDocumentsRequest.loading,
        fetchDocumentsData,
        LINK_DOCUMENT,
    };
}
