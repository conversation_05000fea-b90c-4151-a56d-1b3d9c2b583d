import { useEffect, useState } from "react";
import { IList } from "../../../../../../interfaces/general.interfaces";
import { getImpegniCollegatiGrid } from "../customGrids/grids";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useCalendarData } from "../context/CalendarDataContext";

const DEFAULT_DATA = {
    deadlineUniqueid: "",
    dynamic: "",
    macroInstanceUid: "",
    workingSuspension: true,
    deadlineDate: "",
    weekend: false,
};

export default function useFetchImpegniCollegati() {
    const { t } = useTranslation();
    const { deadlineSaveParams } = useCalendarData();
    const getPreviewDataDeadlineRequest = useGetCustom(
        "archivedeadlines/preview-changes?noTemplateVars=true"
    );
    const [previewDeadlineQuery, setPreviewDeadlineQuery] =
        useState(DEFAULT_DATA);
    const [listImpegniCollegati, setListImpegniCollegati] = useState<
        IList<any>
    >({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });

    useEffect(() => {
        const {
            deadlineUniqueid,
            dynamic,
            macroInstanceUid,
            deadlineDate,
        }: any = deadlineSaveParams;
        setPreviewDeadlineQuery({
            ...previewDeadlineQuery,
            deadlineUniqueid,
            dynamic,
            macroInstanceUid,
            deadlineDate,
        });
    }, [deadlineSaveParams]);

    useEffect(() => {
        const fetchData = async () => {
            const [columns, response]: any = await Promise.all([
                getImpegniCollegatiGrid(t),
                getPreviewDataDeadlineRequest.doFetch(
                    true,
                    previewDeadlineQuery
                ),
            ]);

            const { data } = response;
            setListImpegniCollegati((prev: any) => ({
                ...prev,
                rows: data,
                columns,
            }));
        };

        fetchData();
    }, [previewDeadlineQuery, t]);

    return { listImpegniCollegati };
}
