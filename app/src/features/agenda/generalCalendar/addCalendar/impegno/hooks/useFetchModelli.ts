import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { IList } from "../../../../../../interfaces/general.interfaces";
import { useTranslation } from "@1f/react-sdk";
import { getModelsGrid } from "../customGrids/grids";
import { useNavigate } from "react-router-dom";

const INITIAL_QUERY = {
    page: 0,
    pageSize: 9,
    sortColumn: "",
    sortOrder: "desc",
    templatesIds: "",
};

const LINK_MODELLI = {
    fileUniqueid: "",
};

export default function useFetchModelli(deadlineSaveParams: any) {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const getModelliRequest = useGetCustom(
        "deadlines/get-templates?noTemplateVars=true"
    );
    const [listModelli, setListModelli] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });
    const [modelsQuery, setModelsQuery] = useState<any>(INITIAL_QUERY);

    useEffect(() => {
        setModelsQuery({
            ...modelsQuery,
            templatesIds: deadlineSaveParams.templates,
        });
        LINK_MODELLI.fileUniqueid = deadlineSaveParams.deadlineFileUniqueid;
    }, [deadlineSaveParams.templates]);

    const navigateToModelli = (uniqueid?: string) => {
        if (uniqueid) {
            const URL = `/legacy/archivetemplates/templates?fileUniqueid=${LINK_MODELLI.fileUniqueid}&templateId=${uniqueid}`;
            navigate(URL);
        }
    };

    const fetchModelsData = async () => {
        const [columns, response]: any = await Promise.all([
            getModelsGrid(t),
            getModelliRequest.doFetch(true, modelsQuery),
        ]);

        setListModelli({
            ...listModelli,
            rows: response?.data || [],
            columns,
            totalRows: 0,
            page: modelsQuery?.page,
            pageSize: modelsQuery?.pageSize,
        });
    };

    useEffect(() => {
        fetchModelsData();
    }, [modelsQuery, t]);

    return {
        modelsQuery,
        setModelsQuery,
        listModelli,
        setListModelli,
        loadingModelli: getModelliRequest.loading,
        fetchModelsData,
        navigateToModelli,
    };
}
