import { useEffect, useState } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { IDeadlineParams } from "../interfaces/impegno.interface";
import { DEFAULT_DEADLINE_PARAMS } from "../constants/saveParams";
import { useAgendaParams } from "../../../../hooks/useAgendaParams.hook";

export default function useGetCalendarData(type?: string, idUdienza?: string) {

    const { paramId, fileUniqueid, rowDataUrl} = useAgendaParams();

    const getCalendarDataRequest = useGetCustom("calendar/calendar");
    const getRowDataRequest = usePostCustom(
        type === "agendaDeadline"
            ? "agendadeadlines/getrowdata?noTemplateVars=true"
            : `${rowDataUrl}/getrowdata?noTemplateVars=true`
    );

    const getUsersGroupRequest = useGetCustom(
        "deadlines/getusersgroup?noTemplateVars=true"
    );

    const getFileDataRequest = usePostCustom(
        "deadlines/getfiledata?noTemplateVars=true"
    );

    const [deadlineSaveParams, setDeadlineSaveParams] =
        useState<IDeadlineParams>(DEFAULT_DEADLINE_PARAMS);

    const [selectedPraticaName, setSelectedPraticaName] = useState<any | null>(
        null
    );
    const [recurrenceId, setRecurrenceId] = useState<string | null>(null);

    const [data, setData] = useState<any>({
        usersData: [],
        groupsData: [],
        impegnoData: [],
        tipologiaData: [],
        categoriaData: [],
        statoData: [],
        additionalItems: [],
        expense: [],
        rowData: {}
    });
    const [usersGroupData, setUsersGroupData] = useState<any>([]);
    const [loggedUserName, setLoggedUserDataName] = useState<string>("");

    const getPraticaData = async (idPratica: string) => {
        const response: any = await getFileDataRequest.doFetch(true, {
            idPratica,
        });
        const { data } = response;
        return data;
    };

    const replaceNullWithEmptyString = (obj: any) => {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => [
                key,
                value === null ? "" : value,
            ])
        );
    };

    const updateDefaultDeadlineUser = (data: any) => {
        const checkedUser = data.usersData.find(
            (user: any) => user.id === data?.impegnoData?.deadlineUser?.[0]?.id
        );
        setDeadlineSaveParams((prevParams) => ({
            ...prevParams,
            deadlineUser: [
                {
                    id: checkedUser?.id,
                    nomeutente: checkedUser?.nomeutente,
                },
            ],
        }));
    };

    const updateDefaultDeadlineType = (data: any) => {
        const firstValue = data.tipologiaData[0];
        setDeadlineSaveParams((prevParams) => ({
            ...prevParams,
            deadlineType: firstValue?.id,
        }));
    }

    //inital fetch
    useEffect(() => {
        const fetchData = async () => {
            const [rowDataResponse, calendarResponse]: any = await Promise.all([
                getRowDataRequest.doFetch(true, {
                    fileUniqueid:fileUniqueid ?? idUdienza,
                    uniqueId: paramId
                }),
                getCalendarDataRequest.doFetch(true),
            ]);

            const { form, groups, users } = rowDataResponse.data;
            const {
                deadlineTypes,
                deadlineCategories,
                deadlineStatus,
                items,
                loggedUser,
                listini,
                methods,
                spese_fisse,
            } = calendarResponse.data;

            if (typeof paramId === "string") {
                const transformedForm = {
                    ...replaceNullWithEmptyString(form),
                    deadlineEvasa: form.deadlineEvasa === "1",
                    deadlineNonevadere: form.deadlineNonevadere === "1",
                    deadlineImportant: form.deadlineImportant === "1",
                    deadlineBillable: form.deadlineBillable === "1",
                    deadlineVisible: form.deadlineVisible === "1",
                    deadlineAddebitabile: form.deadlineAddebitabile === "1",
                };

                if (typeof paramId === "string" && form.idPratica !== "") {
                    const praticaData: any = await getPraticaData(
                        form.idPratica
                    );
                    setSelectedPraticaName(praticaData);
                }
                setRecurrenceId(form.recurrenceId);
                setDeadlineSaveParams({
                    ...DEFAULT_DEADLINE_PARAMS,
                    ...form,
                    ...transformedForm,
                });
            }

            setData({
                usersData: users,
                groupsData: groups,
                impegnoData: form,
                tipologiaData: deadlineTypes,
                categoriaData: deadlineCategories,
                statoData: deadlineStatus,
                additionalItems: items,
                listData: listini,
                expenseMethods: methods,
                expenseSpeseFisse: spese_fisse,
                rowData: rowDataResponse.data
            });
            setLoggedUserDataName(loggedUser?.nomeutente);
        };

        fetchData();
    }, [type, paramId, idUdienza]);

    useEffect(() => {
        if (paramId === undefined) {
            updateDefaultDeadlineUser(data);
            updateDefaultDeadlineType(data);
        }
    }, [paramId, data]);

    const fetchUserGroupById = async (id: string) => {
        const response: any = await getUsersGroupRequest.doFetch(true, {
            idGruppo: id,
        });
        const { data } = response;
        setUsersGroupData(data);
        return data;
    };

    //Seperate function to fetch only calendar general data
    const fetchCalendarData = async () => {
        const response: any = await getCalendarDataRequest.doFetch(true);
        const { deadlineTypes, deadlineCategories, deadlineStatus, items } =
            response.data;
        setData((prevData: any) => ({
            ...prevData,
            tipologiaData: deadlineTypes,
            categoriaData: deadlineCategories,
            statoData: deadlineStatus,
            additionalItems: items,
        }));
    };

    return {
        data,
        usersGroupData,
        fetchUserGroupById,
        loggedUserName,
        fetchCalendarData,
        deadlineSaveParams,
        setDeadlineSaveParams,
        selectedPraticaName,
        setSelectedPraticaName,
        isUserEditing: typeof paramId === "string",
        recurrenceId,
        loading: getRowDataRequest.loading || getCalendarDataRequest.loading,
    };
}
