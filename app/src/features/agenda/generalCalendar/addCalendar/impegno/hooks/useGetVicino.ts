import { useState, useEffect } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { getListGrid } from "../customGrids/grids";
import { useTranslation } from "@1f/react-sdk";
import { IList } from "../../../../../../interfaces/general.interfaces";
import { useCalendarData } from "../context/CalendarDataContext";

export default function useGetVicino() {
    const { t } = useTranslation();
    const getVicinoRequest = usePostCustom(
        "itemlist/get-voci-listino?noTemplateVars=true"
    );
    const { data } = useCalendarData();
    const [vicinoListData, setVicinoListData] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });
    const [vicinoSelected, setVicinoSelected] = useState<string | null>(null);

    const fetchVicinoData = async (idListino: string) => {
        const [columns, response]: any = await Promise.all([
            getListGrid(t),
            getVicinoRequest.doFetch(true, {
                idListino,
            }),
        ]);
        const { data } = response;
        if (data.voci !== null) {
            setVicinoListData({
                ...vicinoListData,
                rows: data.voci,
                columns,
                page: 0,
                totalRows: 0,
                pageSize: 10,
            });
        }
    };

    useEffect(() => {
        if (data?.listData !== undefined && vicinoSelected === null) {
            setVicinoSelected(data.listData[0].id_listino);
        }
    }, [vicinoListData, data]);

    useEffect(() => {
        if (vicinoSelected !== null) {
            fetchVicinoData(vicinoSelected);
        }
    }, [vicinoSelected]);

    return {
        vicinoListData,
        setVicinoSelected,
        LIST_OPTIONS: data?.listData || [],
        vicinoSelected,
        loading: getVicinoRequest.loading,
    };
}
