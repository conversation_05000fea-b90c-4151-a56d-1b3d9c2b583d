import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { IList } from "../../../../../../interfaces/general.interfaces";
import { getPrestazioniGrid } from "../customGrids/grids";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useCalendarData } from "../context/CalendarDataContext";

const DEFAULT_QUERY = {
    page: 0,
    pageSize: 9,
    sortColumn: "externalDate",
    sortOrder: "desc",
    deadlineUniqueid: "",
    deadlineLinkuid: "",
    deadlineFileUniqueid: "",
    templates: "",
    dynamic: "",
    macroInstanceUid: "",
    recurrenceId: "",
    connectedElements: "",
    idPratica: "",
    deadlineText: "",
    deadlineDate: "",
    deadlineHours: "",
    deadlineMinutes: "",
    deadlineDaysBefore: "",
    deadlinePeriod: "",
    deadlineOnorariDiritti: "",
    deadlineSpeseesenti: "",
    deadlineSpeseimponibili: "",
    deadlineSpeseescluse: "",
    deadlineType: "",
    deadlineCategory: "",
    deadlineStatus: "",
    deadLinesGroups: "",
    deadlineUser: [],
    deadlineAnnotation: "",
    deadlineAddebitabile: true,
};

export default function usePerformance() {
    const { t } = useTranslation();
    const { deadlineSaveParams, isUserEditing } = useCalendarData();
    const [loading, setLoading] = useState(true);
    const getDeadlineItemListRequest = useGetCustom(
        "deadlines/itemlist?noTemplateVars=true"
    );
    const deletePerformanceRequest = usePostCustom(
        "deadlines/remove-child-item?noTemplateVars=true"
    );

    const [listPerformance, setListPerformance] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });

    const [performanceQuery, setPerformanceQuery] =
        useState<any>(DEFAULT_QUERY);

    useEffect(() => {
        const filteredDeadlineParams = {
            deadlineUniqueid: deadlineSaveParams.deadlineUniqueid,
            deadlineFileUniqueid: deadlineSaveParams.deadlineFileUniqueid,
            idPratica: deadlineSaveParams.idPratica,
            deadlineText: deadlineSaveParams.deadlineText,
            deadlineDate: deadlineSaveParams.deadlineDate,
            deadlineHours: deadlineSaveParams.deadlineHours,
            deadlineMinutes: deadlineSaveParams.deadlineMinutes,
            deadlineDaysBefore: deadlineSaveParams.deadlineDaysBefore,
            deadlinePeriod: deadlineSaveParams.deadlinePeriod,
            deadlineOnorariDiritti: deadlineSaveParams.deadlineOnorariDiritti,
            deadlineSpeseesenti: deadlineSaveParams.deadlineSpeseesenti,
            deadlineSpeseimponibili: deadlineSaveParams.deadlineSpeseimponibili,
            deadlineSpeseescluse: deadlineSaveParams.deadlineSpeseescluse,
            deadlineType: deadlineSaveParams.deadlineType,
            deadlineCategory: deadlineSaveParams.deadlineCategory,
            deadlineStatus: deadlineSaveParams.deadlineStatus,
            deadLinesGroups: deadlineSaveParams.deadLinesGroups,
            deadlineUser: deadlineSaveParams.deadlineUser,
            deadlineAnnotation: deadlineSaveParams.deadlineAnnotation,
            deadlineAddebitabile: deadlineSaveParams.deadlineAddebitabile,
        };

        setPerformanceQuery((prevQuery: any) => ({
            ...prevQuery,
            ...filteredDeadlineParams,
        }));
    }, [deadlineSaveParams]);

    const fetchPrestazioniData = useCallback(async () => {
        const [columns, response]: any = await Promise.all([
            getPrestazioniGrid(handleDeletePerformance, t),
            getDeadlineItemListRequest.doFetch(true, performanceQuery),
        ]);
        const { currentPage, totalRows } = response.data;
        setListPerformance({
            ...listPerformance,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: performanceQuery?.page,
            pageSize: performanceQuery?.pageSize,
        });
        setLoading(false);
    }, [performanceQuery, t]);

    useEffect(() => {
        if (!isUserEditing) return;

        if (performanceQuery.deadlineUniqueid !== "") {
            fetchPrestazioniData();
        } else {
            setLoading(true);
        }
    }, [performanceQuery.deadlineUniqueid]);

    const handleDeletePerformance = async (id: string, type: string) => {
        const formData = new FormData();
        formData.append("id", id);
        formData.append("type", type);

        const response: any = await deletePerformanceRequest.doFetch(
            true,
            formData
        );
        if (response.data.statusCode === 0) {
            fetchPrestazioniData();
        }
    };

    return {
        listPerformance,
        performanceQuery,
        setPerformanceQuery,
        loadingPerformance: loading,
        setLoading,
        fetchPrestazioniData,
    };
}
