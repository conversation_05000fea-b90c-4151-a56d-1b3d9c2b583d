import useGetCustom from "../../../../../../hooks/useGetCustom";

export default function usePrintImpegno() {
    const printRequest = useGetCustom("deadlines/printbyid", {}, null, true);

    const handlePrintRequest = async (uniqueid: string) => {
        if (uniqueid) {
            const response: any = await printRequest.doFetch(true, {
                uniqueid: uniqueid,
            });
            const blob = new Blob([response.data], { type: "text/pdf" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "Impegno_pratica.pdf");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            return true;
        }
    };

    return { handlePrintRequest };
}
