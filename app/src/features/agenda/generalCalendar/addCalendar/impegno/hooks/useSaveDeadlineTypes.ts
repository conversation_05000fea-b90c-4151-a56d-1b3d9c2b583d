import usePostCustom from "../../../../../../hooks/usePostCustom";

export default function useSaveDeadlineTypes() {
    const endpoints = {
        type: "deadlinestypes/remotesave?noTemplateVars=true",
        categoria: "deadlinecategories/remotesave?noTemplateVars=true",
        stato: "deadlinestatus/remotesave?noTemplateVars=true",
    };

    const CreateSaveFunction = (endpoint: string) => {
        const request = usePostCustom(endpoint);
        return async (insertItem: string) => {
            const response: any = await request.doFetch(true, { insertItem });
            return response.data;
        };
    };

    const saveDeadlineType = CreateSaveFunction(endpoints.type);
    const saveDeadlineCategoria = CreateSaveFunction(endpoints.categoria);
    const saveDeadlineStato = CreateSaveFunction(endpoints.stato);

    return { saveDeadlineType, saveDeadlineCategoria, saveDeadlineStato };
}
