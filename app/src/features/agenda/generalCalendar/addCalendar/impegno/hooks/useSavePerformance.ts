import { useEffect, useState } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useCalendarData } from "../context/CalendarDataContext";

interface IDefaultPerformance {
    parentDeadlineUniqueid: string;
    prestazioneSelectedUsers: any;
    tariffPhases: string;
    selectedVoices: string;
    tipo: string;
    externalDate: string;
    prestazioneUser: any;
    nome: string;
    addebitabile: boolean;
    fatturabile: boolean;
    quantita: string;
    duration: string;
    valore: string;
    version: string;
    processed?: string;
    visible?: string;
    contractLimits: boolean;
    contractValue: string;
    costo: string | null;
    type: string;
    iva: string | null;
    deducibilita_iva: string | null;
    paymentMethod: string;
    contractAuthorities?: string | null;
    contractRange?: string | null;
    importedPhases?: any[];
}

export const TYPES = {
    timesheet: 1,
    expense: 2,
    list: 6,
    tariff: 7,
} as const;

const DEFAULT_PARAMS = {
    parentDeadlineUniqueid: "",
    prestazioneSelectedUsers: "",
    tariffPhases: "#",
    selectedVoices: "#",
    tipo: "",
    externalDate: "",
    prestazioneUser: "",
    nome: "",
    addebitabile: true,
    quantita: "1",
    duration: "00:00",
    valore: "0.00",
    version: "2022",
    contractLimits: true,
    contractValue: "",
    costo: "", // in expense default is 0.00
    type: "0",
    iva: "",
    deducibilita_iva: "",
    paymentMethod: "",
    contractAuthorities: null,
    contractRange: null,
    importedPhases: [],
};

const DEFAULT_EXPENSE = {
    costo: "0.00",
    iva: "22.00",
    deducibilita_iva: "0.00",
};

const DEFAULT_TIMESHEET = {
    processed: true,
    visible: false,
    duration: "1:00",
};

export default function useSavePerformance(showPerformance: any) {
    const savePerformanceRequest = usePostCustom(
        "deadlines/save-child-item?noTemplateVars=true"
    );
    const { data } = useCalendarData();
    const [savePerformanceParams, setSavePerformanceParams] =
        useState(DEFAULT_PARAMS);

    //mergin with default values by sections
    useEffect(() => {
        if(showPerformance.timesheet){
            setSavePerformanceParams((prevParams: any) => ({
                ...prevParams,
                ...DEFAULT_TIMESHEET,
            }));
        } else if (showPerformance.expense) {
            setSavePerformanceParams((prevParams: any) => ({
                ...prevParams,
                ...DEFAULT_EXPENSE,
            }));
        }
    }, [savePerformanceParams.tipo]);

    useEffect(() => {
        if (data.impegnoData !== undefined) {
            const {
                deadlineText,
                deadlineUniqueid,
                deadlineUser,
                deadlineDate,
            } = data.impegnoData;

            const defaultUsers = (deadlineUser || [])?.map((defaultUser: any) =>
                data.usersData.find((user: any) => user.id === defaultUser.id)
            );

            setSavePerformanceParams((prevParams: any) => ({
                ...prevParams,
                nome: deadlineText || "",
                parentDeadlineUniqueid: deadlineUniqueid || "",
                externalDate: deadlineDate || "",
                prestazioneSelectedUsers: defaultUsers || "",
                prestazioneUser: defaultUsers || "",
            }));
        }
    }, [data?.impegnoData]);

    const handleSavePerformance = async (params: any) => {
        const formData: any = new FormData();

        Object.keys(params).forEach((key) => {
            const value = params[key as keyof IDefaultPerformance];
            if (value === false) return;

            if (key === "prestazioneUser" && Array.isArray(value)) {
                value.forEach((user: any) => {
                    if (user.id) {
                        formData.append("prestazioneUser", user.id);
                        if (user.hourly_rate) {
                            formData.append(
                                `timesheetRate_${user.id}`,
                                user.hourly_rate
                            );
                        }
                        if (user.costo_risorsa) {
                            formData.append(
                                `costoRisorsa_${user.id}`,
                                user.costo_risorsa
                            );
                        }
                    }
                });
                return;
            }

            if (key === "prestazioneSelectedUsers" && Array.isArray(value)) {
                const ids = value
                    .map((user: any) => user?.id)
                    .filter(Boolean)
                    .join("-");
                if (ids) formData.append("prestazioneSelectedUsers", ids);
                return;
            }

            // Handle `importedPhases` when `params.tipo` is `TYPES.tariff`
            if (
                key === "importedPhases" &&
                params.tipo === TYPES.tariff &&
                Array.isArray(value)
            ) {
                const encodedTariffs = encodeURIComponent(
                    JSON.stringify(value)
                );
                formData.append("tariffPhases", encodedTariffs);
                return; // Skip appending `importedPhases` directly
            }

            // Handle `selectedVoices` when `params.tipo` is `TYPES.list`
            if (
                key === "selectedVoices" &&
                params.tipo === TYPES.list &&
                Array.isArray(value)
            ) {
                const flattenedVoices = value.flat().join(",");
                formData.append("selectedVoices", flattenedVoices);
                return;
            }

            formData.append(key, value);
        });
        const response: any = await savePerformanceRequest.doFetch(
            true,
            formData
        );
        
        const { statusCode } = response.data;
        return statusCode;
    };

    return {
        savePerformanceParams,
        setSavePerformanceParams,
        handleSavePerformance,
    };
}
