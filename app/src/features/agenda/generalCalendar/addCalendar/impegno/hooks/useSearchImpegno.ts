import { useCallback, useState } from "react";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { debounce } from "lodash";

export default function useSearchImpegno() {
    const searchRequest = useGetCustom(
        "default/deadlinestandard/search?noTemplateVars=true"
    );
    const [impegnoSearchResult, setImpegnoSearchResult] = useState<any>([]);

    //search functions
    const debounceSearchImpegno = debounce(async (value: string) => {
        let params = {
            q: value,
        };
        const response: any = await searchRequest.doFetch(true, params);
        setImpegnoSearchResult(response.data);
    }, 500);

    const memoizedSearchPractica = useCallback(debounceSearchImpegno, [
        debounceSearchImpegno,
    ]);

    const handleImpegnoSearch = (value: any) => {
        memoizedSearchPractica(value);
    };

    return {
        impegnoSearchResult,
        handleImpegnoSearch,
        searchImpegnoLoading: searchRequest.loading,
    };
}
