import { useCallback, useState } from "react";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { debounce } from "lodash";

export default function useSearchPractica() {
    const searchPracticaRequest = useGetCustom(
        "/default/archive/search?noTemplateVars=true"
    );
    const [practicaSearchResult, setPracticaSearchResult] = useState<any>([]);
    const [isArchiveChecked, setIsArchiveChecked] = useState<boolean>(false);

    //search functions
    const debounceSearchPractica = debounce(async (value: string) => {
        let params = {
            q: value,
            from: "calendar",
        };
        const response: any = await searchPracticaRequest.doFetch(true, params);
        // Conditionally filter the results if isArchiveChecked is true
        if (isArchiveChecked) {
            response.data = response.data.filter(
                (item: any) => item.is_archived === "1"
            );
        }
        setPracticaSearchResult(response.data);
    }, 500);

    const memoizedSearchPractica = useCallback(debounceSearchPractica, [
        debounceSearchPractica,
    ]);

    const handlePracticaSearch = (value: any) => {
        memoizedSearchPractica(value);
    };

    return {
        practicaSearchResult,
        setPracticaSearchResult,
        handlePracticaSearch,
        searchPracticaLoading: searchPracticaRequest.loading,
        setIsArchiveChecked,
    };
}
