import { useState, useEffect } from "react";
import { ISelectedValue } from "../../../../../../custom-components/TextInputWithClearButton";

const useSessionStorageState = () => {
    const [selectedTipologia, setSelectedTipologia] =
        useState<ISelectedValue | null>(null);
    const [selectedCategoria, setSelectedCategoria] =
        useState<ISelectedValue | null>(null);
    const [selectedStato, setSelectedStato] = useState<ISelectedValue | null>(
        null
    );

    // Load states from sessionStorage on mount
    useEffect(() => {
        const savedTipologia = sessionStorage.getItem("selectedTipologia");
        const savedCategoria = sessionStorage.getItem("selectedCategoria");
        const savedStato = sessionStorage.getItem("selectedStato");

        if (savedTipologia) {
            setSelectedTipologia(JSON.parse(savedTipologia));
        }
        if (savedCategoria) {
            setSelectedCategoria(JSON.parse(savedCategoria));
        }
        if (savedStato) {
            setSelectedStato(JSON.parse(savedStato));
        }
    }, []);

    // Handle sessionStorage for selectedTipologia
    useEffect(() => {
        if (selectedTipologia === null) {
            sessionStorage.removeItem("selectedTipologia");
        } else {
            sessionStorage.setItem(
                "selectedTipologia",
                JSON.stringify(selectedTipologia)
            );
        }
    }, [selectedTipologia]);

    // Handle sessionStorage for selectedCategoria
    useEffect(() => {
        if (selectedCategoria === null) {
            sessionStorage.removeItem("selectedCategoria");
        } else {
            sessionStorage.setItem(
                "selectedCategoria",
                JSON.stringify(selectedCategoria)
            );
        }
    }, [selectedCategoria]);

    // Handle sessionStorage for selectedStato
    useEffect(() => {
        if (selectedStato === null) {
            sessionStorage.removeItem("selectedStato");
        } else {
            sessionStorage.setItem(
                "selectedStato",
                JSON.stringify(selectedStato)
            );
        }
    }, [selectedStato]);

    const clearSessionStorage = () => {
        sessionStorage.removeItem("selectedTipologia");
        sessionStorage.removeItem("selectedCategoria");
        sessionStorage.removeItem("selectedStato");
    };

    return {
        selectedTipologia,
        setSelectedTipologia,
        selectedCategoria,
        setSelectedCategoria,
        selectedStato,
        setSelectedStato,
        clearSessionStorage,
    };
};

export default useSessionStorageState;
