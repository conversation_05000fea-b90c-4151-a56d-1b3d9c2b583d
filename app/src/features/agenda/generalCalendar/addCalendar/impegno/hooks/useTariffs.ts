import { useEffect, useState } from "react";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { formatRangeData } from "../sections/performance/addPerformance/helpers/formatRanges";

export default function useTariffs(
    savePerformanceParams: any,
    setSavePerformanceParams: any
) {
    const getAuthoritiesRequest = useGetCustom(
        "contract/getauthorities?noTemplateVars=true"
    );
    const getRangesRequest = useGetCustom(
        "contract/getranges?noTemplateVars=true"
    );
    const getPhasesRequest = useGetCustom(
        "contract/getphases?noTemplateVars=true"
    );

    const [authorityData, setAuthorityData] = useState<any>([]);
    const [rangeData, setRangeData] = useState<any>([]);
    const [phaseData, setPhaseData] = useState<any>([]);

    
    const fetchAuthoritites = async (version: string | null) => {
        if (version !== "") {
            const response: any = await getAuthoritiesRequest.doFetch(true, {
                version,
            });
            const { data } = response;
            setAuthorityData(data.authorities);
            const DEFAULT_AUTHORITY = data.authorities[0].id;
            if (typeof DEFAULT_AUTHORITY === "string") {
                setSavePerformanceParams({
                    ...savePerformanceParams,
                    contractAuthorities: DEFAULT_AUTHORITY,
                });
            }
        }
    };

    useEffect(() => {
        if (savePerformanceParams.version !== "") {
            fetchAuthoritites(savePerformanceParams.version);
        }
    }, [savePerformanceParams.version]);

    const fetchRanges = async (authorityId: any) => {
        const params = {
            uId: "",
            authorityId,
            customRate: "0",
            year: savePerformanceParams.version,
        };
        const response: any = await getRangesRequest.doFetch(true, params);
        const { data } = response;
        const rangeDataFomated = formatRangeData(data.ranges);
        setRangeData(rangeDataFomated);
        if (rangeDataFomated.length === 0) {
            setSavePerformanceParams({
                ...savePerformanceParams,
                contractRange: "",
            });
        } else {
            const DEFAULT_RANGE = rangeDataFomated[0].id;
            if (
                typeof DEFAULT_RANGE === "string" &&
                rangeDataFomated.length !== 0
            ) {
                setSavePerformanceParams({
                    ...savePerformanceParams,
                    contractRange: DEFAULT_RANGE,
                });
            }
        }
    };

    useEffect(() => {
        if (savePerformanceParams.contractAuthorities !== null) {
            fetchRanges(savePerformanceParams.contractAuthorities);
        }
    }, [savePerformanceParams.contractAuthorities]);

    const fetchPhases = async (rangeValue: string) => {
        const params = {
            uId: "",
            authorityId: savePerformanceParams.contractAuthorities,
            columnName: rangeValue,
            customRate: "0",
            range: rangeValue,
            year: savePerformanceParams.version,
            fileUniqueId: "",
        };
        const response: any = await getPhasesRequest.doFetch(true, params);
        const { data } = response;
        setPhaseData(data);
    };

    useEffect(() => {
        if (savePerformanceParams.contractRange !== null) {
            fetchPhases(savePerformanceParams.contractRange);
        }
    }, [
        savePerformanceParams.contractRange,
        savePerformanceParams.contractAuthorities,
    ]);

    return {
        authorityData,
        rangeData,
        phaseData,
        phaseLoading: getPhasesRequest.loading,
    };
}
