import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../../hooks/useGetCustom";
import { getMergeListArchiveGrid } from "../../../utilities/mergeList/gridColumn";
import { IList } from "../../../interfaces/general.interfaces";

export interface IToMergeListQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    codeType: number;
    code: string;
    customer: string;
    counterpart: string;
}

const defaultQuery: IToMergeListQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "desc",
    codeType: 1, // Hardcoded to 'Codice Archivio'
    code: "",
    customer: "",
    counterpart: "",
};

export const useToMergeListData = () => {
    const { t } = useTranslation();
    const filterRequest = useGetCustom(
        "archive/to-merge-list?noTemplateVars=true"
    );

    const [query, setQuery] = useState<IToMergeListQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    useEffect(() => {
        const filterToMergeList = async () => {
            try {
                const [columns, response]: any = await Promise.all([
                    getMergeListArchiveGrid(t),
                    filterRequest.doFetch(true, query),
                ]);

                const currentPage = Array.isArray(response.data.currentPage)
                    ? response.data.currentPage
                    : [];
                const totalRows = response.data.totalRows || 0;

                const filteredRows = currentPage.filter((row:any) => row !== null && typeof row === 'object');

                if (filteredRows.length !== currentPage.length) {
                    console.warn(`Filtered out ${currentPage.length - filteredRows.length} null rows from response`);
                }

                const newList = {
                    rows: filteredRows,
                    columns,
                    totalRows: parseInt(totalRows) || 0,
                    page: query.page,
                    pageSize: query.pageSize,
                    pageIndex: query.page,
                };

                setList(newList);
            } catch (error) {
                console.error("Error fetching to-merge-list data:", error);
                setList({
                    rows: [],
                    columns: [],
                    totalRows: 0,
                    page: 0,
                    pageSize: 10,
                    pageIndex: 0,
                });
            }
        };

        if (query) {
            filterToMergeList();
        }
    }, [query]);

    const resetFilters = useCallback(() => {
        setQuery(prevQuery => {
            // Only update if the query is actually different from default
            const isAlreadyDefault = JSON.stringify(prevQuery) === JSON.stringify(defaultQuery);
            return isAlreadyDefault ? prevQuery : defaultQuery;
        });
    }, []);

    const updateQuery = useCallback((updates: Partial<IToMergeListQuery>) => {
        setQuery(prevQuery => {
            // Reset to page 0 if anything other than page/pageSize changes
            const resetPage = Object.keys(updates).some(key => 
                key !== 'page' && key !== 'pageSize' && updates[key as keyof IToMergeListQuery] !== prevQuery[key as keyof IToMergeListQuery]
            );
            
            return {
                ...prevQuery,
                ...updates,
                page: resetPage ? 0 : (updates.page !== undefined ? updates.page : prevQuery.page),
            };
        });
    }, []);

    return {
        query,
        setQuery: updateQuery,
        list,
        loading: filterRequest.loading,
        resetFilters,
    };
};
